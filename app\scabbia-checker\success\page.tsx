import { Suspense } from 'react';
import SearchSuccessClient from '@/components/SearchSuccessClient';
import Navbar from '../../components/Navbar';
import Footer from '../../components/Footer';

export default function SuccessPage() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen">
        <Suspense fallback={
          <div className="flex flex-col items-center gap-4 p-8">
            <p>Caricamento...</p>
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-foreground" />
          </div>
        }>
          <SearchSuccessClient />
        </Suspense>
      </main>
      <Footer />
    </>
  );
}
