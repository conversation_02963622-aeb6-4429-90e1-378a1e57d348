<h1 align="center">Scabbia Rimedi</h1>

<p align="center">
Webapp in italiano per valutare i sintomi della scabbia,
gestire un piccolo blog e permettere agli utenti registrati di creare
contenuti protetti.
</p>

<p align="center">
  <a href="#features"><strong>Features</strong></a> ·
  <a href="#demo"><strong>Demo</strong></a> ·
  <a href="#deploy-to-vercel"><strong>Deploy to Vercel</strong></a> ·
  <a href="#clone-and-run-locally"><strong>Clone and run locally</strong></a> ·
  <a href="#pagine-dellapplicazione"><strong>Pagine</strong></a> ·
  <a href="#feedback-and-issues"><strong>Feedback and issues</strong></a>
  <a href="#more-supabase-examples"><strong>More Examples</strong></a>
</p>
<br/>

## Features

- Works across the entire [Next.js](https://nextjs.org) stack
  - App Router
  - Pages Router
  - Middleware
  - Client
  - Server
  - It just works!
- supabase-ssr. A package to configure Supabase Auth to use cookies
- Password-based authentication block installed via the [Supabase UI Library](https://supabase.com/ui/docs/nextjs/password-based-auth)
- Styling with [Tailwind CSS](https://tailwindcss.com)
- Components with [shadcn/ui](https://ui.shadcn.com/)
  - Optional deployment with [Supabase Vercel Integration and Vercel deploy](#deploy-your-own)
  - Environment variables automatically assigned to Vercel project
  - Quiz "Scabbia Checker" con pagamento tramite Stripe
  - Blog con post salvati su Supabase
  - Sistema di autenticazione completo (login, sign up, reset password)
  - Area protetta per creare nuovi articoli
  - Endpoint `/api/ask-ai` invia un JSON al modello "Scabies-Oracle" per una valutazione strutturata

## Pagine dell'applicazione

Di seguito un riepilogo delle pagine presenti nell'applicazione e della loro funzione:

- **Home** – `/` pagina iniziale con introduzione e link alle sezioni principali.
- **Blog** – `/blog` elenco degli articoli.
- **Articolo** – `/blog/[slug]` visualizza il contenuto di un singolo post.
- **Login** – `/auth/login` form di accesso.
- **Registrazione** – `/auth/sign-up` form per creare un nuovo account.
- **Conferma iscrizione** – `/auth/sign-up-success` messaggio dopo la registrazione.
- **Reset password** – `/auth/forgot-password` invio della mail di recupero.
- **Nuova password** – `/auth/update-password` per impostare una nuova password.
- **Errore autenticazione** – `/auth/error` mostra eventuali errori di login.
- **Area protetta** – `/protected` pagina accessibile solo agli utenti loggati.
- **Nuovo post** – `/protected/new-post` consente di pubblicare un articolo (richiede login).
- **Scabbia Checker** – `/scabbia-checker` questionario sui sintomi con pagamento Stripe.
- **Pagamento annullato** – `/scabbia-checker/cancel` pagina mostrata se il pagamento non va a buon fine.
- **Risultato** – `/scabbia-checker/success` visualizza l'esito del questionario dopo il pagamento.

## Demo

You can view a fully working demo at [demo-nextjs-with-supabase.vercel.app](https://demo-nextjs-with-supabase.vercel.app/).

## Deploy to Vercel

Vercel deployment will guide you through creating a Supabase account and project.

After installation of the Supabase integration, all relevant environment variables will be assigned to the project so the deployment is fully functioning.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&project-name=nextjs-with-supabase&repository-name=nextjs-with-supabase&demo-title=nextjs-with-supabase&demo-description=This+starter+configures+Supabase+Auth+to+use+cookies%2C+making+the+user%27s+session+available+throughout+the+entire+Next.js+app+-+Client+Components%2C+Server+Components%2C+Route+Handlers%2C+Server+Actions+and+Middleware.&demo-url=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2F&external-id=https%3A%2F%2Fgithub.com%2Fvercel%2Fnext.js%2Ftree%2Fcanary%2Fexamples%2Fwith-supabase&demo-image=https%3A%2F%2Fdemo-nextjs-with-supabase.vercel.app%2Fopengraph-image.png)

The above will also clone the Starter kit to your GitHub, you can clone that locally and develop locally.

If you wish to just develop locally and not deploy to Vercel, [follow the steps below](#clone-and-run-locally).

## Clone and run locally

1. You'll first need a Supabase project which can be made [via the Supabase dashboard](https://database.new)

2. Create a Next.js app using the Supabase Starter template npx command

   ```bash
   npx create-next-app --example with-supabase with-supabase-app
   ```

   ```bash
   yarn create next-app --example with-supabase with-supabase-app
   ```

   ```bash
   pnpm create next-app --example with-supabase with-supabase-app
   ```

3. Use `cd` to change into the app's directory

   ```bash
   cd with-supabase-app
   ```

4. Rename `.env.example` to `.env.local` and update the following:

   ```
   NEXT_PUBLIC_SUPABASE_URL=[INSERT SUPABASE PROJECT URL]
   NEXT_PUBLIC_SUPABASE_ANON_KEY=[INSERT SUPABASE PROJECT API ANON KEY]
   STRIPE_SECRET_KEY=[YOUR STRIPE SECRET KEY]
   STRIPE_PRODUCT_ID=prod_SRgEK7RRt1js7O
   AI_MODEL=chatpgt-4.1-nano
   OPENAI_API_KEY=[YOUR OPENAI API KEY]
   ```

   Both `NEXT_PUBLIC_SUPABASE_URL` and `NEXT_PUBLIC_SUPABASE_ANON_KEY` can be found in [your Supabase project's API settings](https://supabase.com/dashboard/project/_?showConnect=true)

5. You can now run the Next.js local development server:

   ```bash
   npm run dev
   ```

   The starter kit should now be running on [localhost:3000](http://localhost:3000/).

6. This template comes with the default shadcn/ui style initialized. If you instead want other ui.shadcn styles, delete `components.json` and [re-install shadcn/ui](https://ui.shadcn.com/docs/installation/next)

> Check out [the docs for Local Development](https://supabase.com/docs/guides/getting-started/local-development) to also run Supabase locally.

## Configurare Supabase per il blog

Per permettere agli utenti autenticati di creare nuovi articoli è necessario predisporre il database Supabase con una tabella `posts` e abilitare le policy corrette.

1. Nel progetto Supabase creare la tabella `posts` con i seguenti campi:
   - `id` integer primary key con `generated by default as identity`
   - `slug` text unico
   - `title` text
   - `excerpt` text
   - `content` text
   - `created_at` timestamp con valore di default `now()`
2. Abilitare il Row Level Security sulla tabella e creare una policy `INSERT` che consenta l'inserimento agli utenti autenticati (`auth.uid() IS NOT NULL`).
3. Registrare un nuovo utente tramite la pagina di **sign up** dell'applicazione oppure dal pannello Supabase. Una volta effettuato l'accesso, l'utente potrà visitare `/protected/new-post` e pubblicare un articolo.

Per verificare che la configurazione sia corretta è possibile eseguire un inserimento di prova dal pannello Supabase o dall'applicazione. Se l'operazione riesce e le variabili `NEXT_PUBLIC_SUPABASE_URL` e `NEXT_PUBLIC_SUPABASE_ANON_KEY` sono impostate, l'ambiente è pronto per ricevere nuovi post.

## Feedback and issues

Please file feedback and issues over on the [Supabase GitHub org](https://github.com/supabase/supabase/issues/new/choose).

## More Supabase examples

- [Next.js Subscription Payments Starter](https://github.com/vercel/nextjs-subscription-payments)
- [Cookie-based Auth and the Next.js 13 App Router (free course)](https://youtube.com/playlist?list=PL5S4mPUpp4OtMhpnp93EFSo42iQ40XjbF)
- [Supabase Auth and the Next.js App Router](https://github.com/supabase/supabase/tree/master/examples/auth/nextjs)
