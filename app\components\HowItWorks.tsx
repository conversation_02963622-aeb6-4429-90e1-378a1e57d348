import { Search, Brain, FileText, CheckCircle, ArrowRight, Clock } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

const steps = [
  {
    icon: Search,
    title: 'Compila il questionario',
    description: 'Rispondi a domande mirate sui tuoi sintomi. Il nostro sistema AI analizza ogni risposta.',
    duration: '2 min',
    step: '01'
  },
  {
    icon: Brain,
    title: 'Analisi AI avanzata',
    description: 'La nostra intelligenza artificiale elabora i dati e confronta i sintomi con il database medico.',
    duration: '30 sec',
    step: '02'
  },
  {
    icon: FileText,
    title: 'Ricevi la valutazione',
    description: 'Ottieni un report dettagliato con la probabilità di scabbia e raccomandazioni personalizzate.',
    duration: '1 min',
    step: '03'
  },
  {
    icon: CheckCircle,
    title: 'Segui i consigli',
    description: 'Ricevi consigli medici specifici e indicazioni su quando consultare un professionista.',
    duration: 'Immediato',
    step: '04'
  }
]

export default function HowItWorks() {
  return (
    <section id="come-funziona" className="py-20 bg-background">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="mb-4">
            Processo Semplice
          </Badge>
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground">
            Come Funziona il Test
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Un processo semplice e veloce che ti guida passo dopo passo verso una valutazione
            accurata dei tuoi sintomi.
          </p>
        </div>

        {/* Steps */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
          {steps.map((step, index) => (
            <div key={index} className="relative">
              {/* Connection line (hidden on mobile, shown on larger screens) */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-primary/50 to-transparent z-0">
                  <div className="absolute right-0 top-1/2 transform -translate-y-1/2">
                    <ArrowRight className="w-4 h-4 text-primary/50" />
                  </div>
                </div>
              )}

              <Card className="relative z-10 h-full hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
                <CardContent className="p-6 text-center space-y-4">
                  {/* Step number */}
                  <div className="flex justify-center mb-4">
                    <Badge variant="outline" className="text-xs font-mono">
                      {step.step}
                    </Badge>
                  </div>

                  {/* Icon */}
                  <div className="relative mx-auto">
                    <div className="w-16 h-16 mx-auto bg-gradient-to-br from-primary to-primary-dark rounded-2xl flex items-center justify-center shadow-medium">
                      <step.icon className="w-8 h-8 text-white" />
                    </div>
                    {/* Decorative ring */}
                    <div className="absolute inset-0 w-16 h-16 mx-auto border-2 border-primary/20 rounded-2xl animate-pulse-soft"></div>
                  </div>

                  {/* Content */}
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold text-foreground">
                      {step.title}
                    </h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {step.description}
                    </p>
                  </div>

                  {/* Duration */}
                  <div className="flex items-center justify-center text-xs text-muted-foreground pt-2">
                    <Clock className="w-3 h-3 mr-1" />
                    {step.duration}
                  </div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="inline-flex flex-col sm:flex-row items-center gap-4 p-6 bg-gradient-to-r from-primary/5 to-secondary-accent/5 rounded-2xl border border-primary/10">
            <div className="text-center sm:text-left">
              <h3 className="text-lg font-semibold text-foreground mb-1">
                Pronto per iniziare?
              </h3>
              <p className="text-sm text-muted-foreground">
                Il test è completamente gratuito e richiede solo pochi minuti.
              </p>
            </div>
            <a
              href="/scabbia-checker"
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-primary to-primary-dark text-white rounded-lg font-medium hover:from-primary-dark hover:to-primary transition-all shadow-medium hover:shadow-large group"
            >
              Inizia Ora
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

