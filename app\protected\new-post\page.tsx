import { redirect } from "next/navigation";
import { createClient } from "@/lib/supabase/server";
import { NewPostForm } from "@/components/new-post-form";

export default async function NewPostPage() {
  const supabase = await createClient();
  const { data, error } = await supabase.auth.getUser();

  if (error || !data?.user) {
    redirect("/auth/login");
  }

  return (
    <div className="max-w-2xl mx-auto py-10 px-4">
      <NewPostForm />
    </div>
  );
}
