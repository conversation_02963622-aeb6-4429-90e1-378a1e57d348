import Link from 'next/link'
import { Stethoscope, Mail, Phone, MapPin, Heart } from 'lucide-react'

const footerLinks = {
  informazioni: [
    { name: "Cos'è la scabbia", href: '/cos-e-la-scabbia' },
    { name: '<PERSON>tom<PERSON>', href: '/sintomi' },
    { name: 'Cause', href: '/cause' },
    { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/diagnosi' },
  ],
  trattamento: [
    { name: 'C<PERSON>', href: '/cura' },
    { name: 'Prevenzione', href: '/prevenzione' },
    { name: 'Blog', href: '/blog' },
  ],
  supporto: [
    { name: '<PERSON><PERSON><PERSON>', href: '/contatti' },
    { name: 'FAQ', href: '/faq' },
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Termini di Servizio', href: '/termini' },
  ]
}

const contactInfo = [
  {
    icon: Mail,
    label: 'Email',
    value: '<EMAIL>',
    href: 'mailto:<EMAIL>'
  },
  {
    icon: Phone,
    label: 'Telefono',
    value: '+39 02 1234 5678',
    href: 'tel:+390212345678'
  },
  {
    icon: MapPin,
    label: 'Indirizzo',
    value: 'Milano, Italia',
    href: '#'
  }
]

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-muted/30 border-t">
      <div className="container-custom">
        {/* Main footer content */}
        <div className="py-16">
          <div className="grid gap-12 lg:grid-cols-4">
            {/* Brand section */}
            <div className="lg:col-span-1 space-y-6">
              <Link href="/" className="flex items-center space-x-2">
                <Stethoscope className="h-8 w-8 text-primary" />
                <span className="text-xl font-bold text-foreground">ScabbiaSintomi</span>
              </Link>

              <p className="text-muted-foreground leading-relaxed">
                Il test AI più accurato per identificare i sintomi della scabbia.
                Informazioni mediche affidabili e consigli personalizzati.
              </p>

              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Heart className="w-4 h-4 text-red-500" />
                <span>Realizzato con cura per la tua salute</span>
              </div>
            </div>

            {/* Links sections */}
            <div className="lg:col-span-2 grid gap-8 sm:grid-cols-3">
              <div>
                <h3 className="font-semibold text-foreground mb-4">Informazioni</h3>
                <ul className="space-y-3">
                  {footerLinks.informazioni.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-muted-foreground hover:text-primary transition-colors text-sm"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-foreground mb-4">Trattamento</h3>
                <ul className="space-y-3">
                  {footerLinks.trattamento.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-muted-foreground hover:text-primary transition-colors text-sm"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div>
                <h3 className="font-semibold text-foreground mb-4">Supporto</h3>
                <ul className="space-y-3">
                  {footerLinks.supporto.map((link) => (
                    <li key={link.name}>
                      <Link
                        href={link.href}
                        className="text-muted-foreground hover:text-primary transition-colors text-sm"
                      >
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Contact section */}
            <div className="lg:col-span-1">
              <h3 className="font-semibold text-foreground mb-4">Contatti</h3>
              <div className="space-y-4">
                {contactInfo.map((contact) => (
                  <div key={contact.label} className="flex items-start space-x-3">
                    <contact.icon className="w-4 h-4 text-primary mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-xs text-muted-foreground">{contact.label}</p>
                      {contact.href !== '#' ? (
                        <a
                          href={contact.href}
                          className="text-sm text-foreground hover:text-primary transition-colors"
                        >
                          {contact.value}
                        </a>
                      ) : (
                        <p className="text-sm text-foreground">{contact.value}</p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="py-6 border-t border-border">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <div className="text-sm text-muted-foreground">
              © {currentYear} ScabbiaSintomi.it - Tutti i diritti riservati
            </div>

            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <Link href="/privacy" className="hover:text-primary transition-colors">
                Privacy
              </Link>
              <Link href="/termini" className="hover:text-primary transition-colors">
                Termini
              </Link>
              <Link href="/cookie" className="hover:text-primary transition-colors">
                Cookie
              </Link>
            </div>
          </div>
        </div>

        {/* Medical disclaimer */}
        <div className="py-4 border-t border-border">
          <div className="text-xs text-muted-foreground text-center leading-relaxed">
            <strong>Disclaimer medico:</strong> Le informazioni fornite su questo sito sono solo a scopo educativo e non sostituiscono
            il parere medico professionale. Consulta sempre un medico per diagnosi e trattamenti specifici.
          </div>
        </div>
      </div>
    </footer>
  )
}

