@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern Medical Color Palette */
    --background: 0 0% 100%;
    --foreground: 210 40% 8%;
    --card: 0 0% 100%;
    --card-foreground: 210 40% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 40% 8%;

    /* Primary - Medical Blue */
    --primary: 210 100% 50%;
    --primary-foreground: 0 0% 98%;
    --primary-light: 210 100% 95%;
    --primary-dark: 210 100% 40%;

    /* Secondary - Teal */
    --secondary: 180 25% 95%;
    --secondary-foreground: 180 25% 15%;
    --secondary-accent: 180 84% 60%;

    /* Success - Green */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --success-light: 142 76% 95%;

    /* Warning - Amber */
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --warning-light: 38 92% 95%;

    /* Error - Red */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --destructive-light: 0 84% 95%;

    /* Neutral Colors */
    --muted: 210 40% 98%;
    --muted-foreground: 210 40% 45%;
    --accent: 210 40% 96%;
    --accent-foreground: 210 40% 10%;

    /* Border & Input */
    --border: 210 40% 90%;
    --input: 210 40% 90%;
    --ring: 210 100% 50%;

    /* Chart Colors */
    --chart-1: 210 100% 50%;
    --chart-2: 180 84% 60%;
    --chart-3: 142 76% 36%;
    --chart-4: 38 92% 50%;
    --chart-5: 0 84% 60%;

    /* Border Radius */
    --radius: 0.75rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  }

  .dark {
    --background: 210 40% 3%;
    --foreground: 210 40% 98%;
    --card: 210 40% 4%;
    --card-foreground: 210 40% 98%;
    --popover: 210 40% 4%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 100% 60%;
    --primary-foreground: 210 40% 3%;
    --primary-light: 210 100% 10%;
    --primary-dark: 210 100% 70%;

    --secondary: 210 40% 10%;
    --secondary-foreground: 210 40% 98%;
    --secondary-accent: 180 84% 50%;

    --success: 142 76% 46%;
    --success-foreground: 210 40% 3%;
    --success-light: 142 76% 10%;

    --warning: 38 92% 60%;
    --warning-foreground: 210 40% 3%;
    --warning-light: 38 92% 10%;

    --destructive: 0 84% 70%;
    --destructive-foreground: 210 40% 3%;
    --destructive-light: 0 84% 10%;

    --muted: 210 40% 10%;
    --muted-foreground: 210 40% 65%;
    --accent: 210 40% 10%;
    --accent-foreground: 210 40% 98%;

    --border: 210 40% 18%;
    --input: 210 40% 18%;
    --ring: 210 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Focus styles */
  *:focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-primary-foreground;
  }
}

@layer components {
  /* Container utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Text utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-secondary-accent bg-clip-text text-transparent;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.3s ease-out;
  }

  .animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce {
    animation: bounce 1s infinite;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }

  .animate-progress {
    animation: progressFill 0.8s ease-out;
  }
}

@layer utilities {
  /* Container utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Custom shadows */
  .shadow-soft {
    box-shadow: var(--shadow);
  }

  .shadow-medium {
    box-shadow: var(--shadow-md);
  }

  .shadow-large {
    box-shadow: var(--shadow-lg);
  }

  .shadow-extra-large {
    box-shadow: var(--shadow-xl);
  }

  /* Glass effect */
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  .glass-dark {
    @apply bg-black/80 backdrop-blur-sm border border-white/10;
  }

  /* Text utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Blog Article Styles */
@layer components {
  .prose {
    @apply text-foreground max-w-none;
  }

  .prose h1 {
    @apply text-3xl font-bold text-foreground mt-8 mb-4 leading-tight;
  }

  .prose h2 {
    @apply text-2xl font-semibold text-foreground mt-8 mb-4 leading-tight;
  }

  .prose h3 {
    @apply text-xl font-semibold text-foreground mt-6 mb-3 leading-tight;
  }

  .prose h4 {
    @apply text-lg font-semibold text-foreground mt-6 mb-3 leading-tight;
  }

  .prose p {
    @apply text-muted-foreground leading-relaxed mb-4;
  }

  .prose a {
    @apply text-primary hover:text-primary-dark underline underline-offset-2 transition-colors;
  }

  .prose strong {
    @apply text-foreground font-semibold;
  }

  .prose em {
    @apply text-muted-foreground italic;
  }

  .prose ul, .prose ol {
    @apply text-muted-foreground mb-4 pl-6;
  }

  .prose li {
    @apply mb-2;
  }

  .prose blockquote {
    @apply border-l-4 border-primary pl-4 italic text-muted-foreground bg-muted/50 py-2 my-6 rounded-r-lg;
  }

  .prose code {
    @apply text-primary bg-muted px-1 py-0.5 rounded text-sm font-mono;
  }

  .prose pre {
    @apply bg-muted p-4 rounded-lg overflow-x-auto text-sm font-mono mb-4;
  }

  .prose img {
    @apply rounded-lg shadow-medium my-6 w-full;
  }

  .prose table {
    @apply w-full border-collapse border border-border my-6;
  }

  .prose th, .prose td {
    @apply border border-border px-4 py-2 text-left;
  }

  .prose th {
    @apply bg-muted font-semibold text-foreground;
  }

  .prose td {
    @apply text-muted-foreground;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes progressFill {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0%);
  }
}
