import type { Metada<PERSON> } from "next";
import { getAllPosts } from "@/lib/blog";
import { Badge } from "@/components/ui/badge";
import { BookOpen } from "lucide-react";
import Navbar from "@/app/components/Navbar";
import Footer from "@/app/components/Footer";
import BlogSearch from "@/components/blog-search";

export const metadata: Metadata = {
  title: "Blog | Scabbia Rimedi - Articoli e Guide Mediche",
  description: "Scopri articoli approfonditi sulla scabbia: sintomi, trattamenti, prevenzione e consigli medici. Guide complete scritte da esperti.",
  keywords: "blog scabbia, articoli medici, guide trattamento, sintomi scabbia, prevenzione",
};



export default async function BlogPage() {
  const posts = await getAllPosts();

  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                <BookOpen className="w-4 h-4 mr-2" />
                Risorse Mediche
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                <span className="text-gradient">Blog</span> e Guide Mediche
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                Articoli approfonditi e guide complete sulla scabbia, scritti da esperti medici
                per aiutarti a comprendere meglio sintomi, trattamenti e prevenzione.
              </p>
            </div>
          </div>
        </section>

        {/* Articles with Search */}
        <section className="py-16">
          <div className="container-custom">
            <BlogSearch posts={posts} />
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
