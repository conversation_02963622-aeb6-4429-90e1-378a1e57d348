'use client'

import type { Metadata } from 'next'
import { useState } from 'react'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { ChevronDown, ChevronUp, HelpCircle, Stethoscope, Shield, Clock } from 'lucide-react'

const faqCategories = [
  {
    icon: HelpCircle,
    title: 'Domande Generali',
    faqs: [
      {
        question: "Cos'è ScabbiaSintomi.it?",
        answer: "ScabbiaSintomi.it è una piattaforma informativa che fornisce strumenti di valutazione dei sintomi della scabbia basati su intelligenza artificiale e criteri clinici standardizzati. Il nostro obiettivo è fornire informazioni educative affidabili per aiutare le persone a comprendere meglio questa condizione."
      },
      {
        question: "Il servizio è gratuito?",
        answer: "Le informazioni educative e la maggior parte dei contenuti sono gratuiti. Il test di valutazione avanzato dei sintomi richiede un piccolo contributo per coprire i costi dell'analisi AI e mantenere il servizio di alta qualità."
      },
      {
        question: "Chi gestisce il sito?",
        answer: "Il sito è gestito da un team di esperti in ambito sanitario e tecnologico, con la supervisione di professionisti medici per garantire l'accuratezza delle informazioni fornite."
      }
    ]
  },
  {
    icon: Stethoscope,
    title: 'Aspetti Medici',
    faqs: [
      {
        question: "Il test può sostituire una visita medica?",
        answer: "Assolutamente no. Il nostro test è uno strumento informativo che può aiutare a comprendere i sintomi, ma non sostituisce mai la consulenza di un medico qualificato. Per qualsiasi problema di salute, consulta sempre un professionista sanitario."
      },
      {
        question: "Quanto è accurato il test di valutazione?",
        answer: "Il test utilizza algoritmi basati sui criteri diagnostici IACS 2020 e le linee guida CDC. Tuttavia, l'accuratezza dipende dalla precisione delle risposte fornite e non può mai essere considerata una diagnosi definitiva."
      },
      {
        question: "Cosa devo fare se il test indica un alto rischio?",
        answer: "Se il test indica un alto rischio di scabbia, ti consigliamo vivamente di consultare un dermatologo o il tuo medico di base il prima possibile per una valutazione clinica professionale e, se necessario, per iniziare un trattamento appropriato."
      },
      {
        question: "Il test è adatto ai bambini?",
        answer: "Il test è progettato per adulti. Per i bambini, è sempre necessaria una valutazione medica diretta. Se sospetti che un bambino possa avere la scabbia, consulta immediatamente un pediatra."
      }
    ]
  },
  {
    icon: Shield,
    title: 'Privacy e Sicurezza',
    faqs: [
      {
        question: "I miei dati sono al sicuro?",
        answer: "Sì, prendiamo molto seriamente la privacy dei nostri utenti. Tutti i dati sono crittografati e trattati in conformità al GDPR. Le risposte al test sono anonime e utilizzate solo per generare la valutazione."
      },
      {
        question: "Conservate le mie risposte al test?",
        answer: "Le risposte al test vengono elaborate per generare la valutazione e poi eliminate. Non conserviamo dati personali identificabili legati alle risposte del test."
      },
      {
        question: "Posso richiedere la cancellazione dei miei dati?",
        answer: "Sì, hai il diritto di richiedere la cancellazione dei tuoi dati in qualsiasi momento. Contattaci all'indirizzo <EMAIL> per esercitare questo diritto."
      }
    ]
  },
  {
    icon: Clock,
    title: 'Supporto Tecnico',
    faqs: [
      {
        question: "Il sito non funziona correttamente, cosa posso fare?",
        answer: "Prova prima a svuotare la cache del browser e ricaricare la pagina. Se il problema persiste, contattaci attraverso il modulo di contatto fornendo dettagli sul problema riscontrato e il browser utilizzato."
      },
      {
        question: "Posso utilizzare il sito su dispositivi mobili?",
        answer: "Sì, il sito è completamente ottimizzato per dispositivi mobili e tablet. Puoi accedere a tutte le funzionalità da qualsiasi dispositivo con connessione internet."
      },
      {
        question: "Quanto tempo richiede il test di valutazione?",
        answer: "Il test richiede solitamente 3-5 minuti per essere completato. Include 10 domande sui sintomi e la generazione del rapporto di valutazione avviene in tempo reale."
      },
      {
        question: "Posso salvare o stampare i risultati del test?",
        answer: "Sì, puoi scaricare i risultati in formato PDF o condividerli. Ti consigliamo di salvare una copia per mostrarla al tuo medico durante la consultazione."
      }
    ]
  }
]

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<Record<string, boolean>>({})

  const toggleItem = (categoryIndex: number, faqIndex: number) => {
    const key = `${categoryIndex}-${faqIndex}`
    setOpenItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                Domande Frequenti
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                <span className="text-gradient">FAQ</span>
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Trova risposte alle domande più comuni su ScabbiaSintomi.it e sul nostro servizio
              </p>
            </div>
          </div>
        </section>

        {/* FAQ Content */}
        <section className="py-16">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto space-y-12">
              {faqCategories.map((category, categoryIndex) => (
                <div key={categoryIndex}>
                  <div className="flex items-center gap-3 mb-8">
                    <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                      <category.icon className="w-5 h-5 text-primary" />
                    </div>
                    <h2 className="text-2xl font-bold text-foreground">{category.title}</h2>
                  </div>
                  
                  <div className="space-y-4">
                    {category.faqs.map((faq, faqIndex) => {
                      const key = `${categoryIndex}-${faqIndex}`
                      const isOpen = openItems[key]
                      
                      return (
                        <Card key={faqIndex} className="border-0 shadow-medium bg-card/50 backdrop-blur-sm overflow-hidden">
                          <button
                            className="w-full p-6 text-left hover:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors duration-200"
                            onClick={() => toggleItem(categoryIndex, faqIndex)}
                            aria-expanded={isOpen}
                            aria-controls={`faq-${key}`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-semibold text-foreground text-left pr-4">{faq.question}</span>
                              {isOpen ? (
                                <ChevronUp className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                              ) : (
                                <ChevronDown className="w-5 h-5 text-muted-foreground flex-shrink-0" />
                              )}
                            </div>
                          </button>
                          
                          {isOpen && (
                            <div id={`faq-${key}`} className="px-6 pb-6">
                              <div className="pt-4 border-t border-border">
                                <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                              </div>
                            </div>
                          )}
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ))}
            </div>

            {/* Contact Section */}
            <div className="max-w-4xl mx-auto mt-16">
              <Card className="p-8 text-center border-0 shadow-medium bg-gradient-to-r from-primary/5 to-secondary/5 backdrop-blur-sm">
                <h3 className="text-2xl font-bold text-foreground mb-4">Non hai trovato la risposta che cercavi?</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed">
                  Se hai altre domande o hai bisogno di assistenza specifica, non esitare a contattarci. 
                  Il nostro team è qui per aiutarti.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a 
                    href="/contatti" 
                    className="inline-block bg-primary text-primary-foreground px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
                  >
                    Contattaci
                  </a>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="inline-block bg-muted text-foreground px-6 py-3 rounded-lg font-medium hover:bg-muted/80 transition-colors"
                  >
                    Invia Email
                  </a>
                </div>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  )
}
