'use client';

import { useState } from 'react';
import { Mail, Phone, MapPin, Clock, ChevronDown, AlertTriangle, Facebook, Twitter, Instagram } from 'lucide-react';
import ContactForm from '../components/ContactForm';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

type FAQItem = {
  question: string;
  answer: string;
};

const contactInfo = [
  {
    icon: Mail,
    title: 'Email',
    value: '<EMAIL>',
    description: 'Rispondiamo entro 24-48 ore',
    href: 'mailto:<EMAIL>'
  },
  {
    icon: Phone,
    title: 'Telefono',
    value: '+39 02 1234 5678',
    description: 'Lun-Ven, 9:00-18:00',
    href: 'tel:+390212345678'
  },
  {
    icon: MapPin,
    title: 'Sede legale',
    value: 'Via Roma 123, 20121 Milano',
    description: 'Italia',
    href: '#'
  }
];

const socialLinks = [
  {
    name: 'Facebook',
    icon: Facebook,
    href: 'https://facebook.com',
    color: 'hover:text-blue-600'
  },
  {
    name: 'Twitter',
    icon: Twitter,
    href: 'https://twitter.com',
    color: 'hover:text-blue-400'
  },
  {
    name: 'Instagram',
    icon: Instagram,
    href: 'https://instagram.com',
    color: 'hover:text-pink-600'
  }
];

const faqs: FAQItem[] = [
  {
    question: "Quanto tempo ci vuole per ricevere una risposta?",
    answer: "Cerchiamo di rispondere a tutte le richieste entro 24-48 ore lavorative. Le richieste ricevute nel fine settimana verranno gestite il lunedì successivo."
  },
  {
    question: "Fornite consulenze mediche personalizzate?",
    answer: "No, non forniamo consulenze mediche personalizzate. Le informazioni su questo sito hanno solo scopo informativo e non sostituiscono il parere del medico. Per problemi di salute specifici, ti consigliamo di consultare un professionista sanitario qualificato."
  },
  {
    question: "Come posso contribuire al sito?",
    answer: "Se desideri condividere la tua esperienza o suggerire miglioramenti, puoi utilizzare il modulo di contatto. Siamo sempre aperti a storie personali e suggerimenti da parte dei nostri lettori."
  },
  {
    question: "Il sito è gestito da professionisti medici?",
    answer: "Il contenuto del sito è stato sviluppato con la consulenza di professionisti medici e ricercato da esperti del settore sanitario. Tuttavia, le informazioni fornite non costituiscono una relazione medico-paziente."
  }
];

export default function ContattiPage() {
  const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);

  const toggleFaq = (index: number) => {
    setOpenFaqIndex(openFaqIndex === index ? null : index);
  };

  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                Supporto
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                <span className="text-gradient">Contattaci</span>
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Hai domande sulla scabbia? Vuoi condividere la tua esperienza o segnalare un problema?
                Siamo qui per aiutarti.
              </p>
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-20">
          <div className="container-custom">
            <div className="grid lg:grid-cols-2 gap-16 items-start">
              {/* Contact Form */}
              <div className="space-y-8">
                <div className="text-center lg:text-left">
                  <h2 className="text-3xl font-bold text-foreground mb-4">
                    Inviaci un messaggio
                  </h2>
                  <p className="text-muted-foreground">
                    Compila il modulo e ti risponderemo il prima possibile.
                  </p>
                </div>
                <ContactForm />
              </div>

              {/* Contact Info */}
              <div className="space-y-8">
                <div className="text-center lg:text-left">
                  <h2 className="text-3xl font-bold text-foreground mb-4">
                    Informazioni di contatto
                  </h2>
                  <p className="text-muted-foreground">
                    Puoi raggiungerci attraverso questi canali.
                  </p>
                </div>

                <div className="space-y-6">
                  {contactInfo.map((contact, index) => (
                    <Card key={index} className="border-0 shadow-medium bg-card/50 backdrop-blur-sm hover:shadow-large transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4">
                          <div className="w-12 h-12 bg-gradient-to-br from-primary to-primary-dark rounded-xl flex items-center justify-center flex-shrink-0">
                            <contact.icon className="w-6 h-6 text-white" />
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold text-foreground mb-1">{contact.title}</h3>
                            {contact.href !== '#' ? (
                              <a
                                href={contact.href}
                                className="text-primary hover:text-primary-dark transition-colors font-medium"
                              >
                                {contact.value}
                              </a>
                            ) : (
                              <p className="text-foreground font-medium">{contact.value}</p>
                            )}
                            <p className="text-sm text-muted-foreground mt-1">{contact.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Social Links */}
                <Card className="border-0 shadow-medium bg-card/50 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-foreground mb-4">Seguici sui social</h3>
                    <div className="flex space-x-4">
                      {socialLinks.map((social) => (
                        <a
                          key={social.name}
                          href={social.href}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`w-10 h-10 bg-muted rounded-lg flex items-center justify-center text-muted-foreground transition-colors ${social.color}`}
                          aria-label={`${social.name} (si apre in una nuova scheda)`}
                        >
                          <social.icon className="w-5 h-5" />
                        </a>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>
        {/* FAQ Section */}
        <section className="py-20 bg-muted/30">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-foreground mb-4">
                  Domande Frequenti
                </h2>
                <p className="text-muted-foreground">
                  Le risposte alle domande più comuni sui nostri servizi.
                </p>
              </div>

              <div className="space-y-4">
                {faqs.map((faq, index) => (
                  <Card key={index} className="border-0 shadow-medium bg-card/50 backdrop-blur-sm overflow-hidden">
                    <button
                      className="w-full p-6 text-left hover:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-colors duration-200"
                      onClick={() => toggleFaq(index)}
                      aria-expanded={openFaqIndex === index}
                      aria-controls={`faq-${index}`}
                      aria-label={`${faq.question}. ${openFaqIndex === index ? 'Chiudi la risposta' : 'Mostra la risposta'}`}
                    >
                      <div className="flex items-center justify-between">
                        <span className="font-semibold text-foreground text-left pr-4">{faq.question}</span>
                        <ChevronDown
                          className={`h-5 w-5 text-muted-foreground transform transition-transform duration-200 flex-shrink-0 ${
                            openFaqIndex === index ? 'rotate-180' : ''
                          }`}
                        />
                      </div>
                    </button>
                    <div
                      id={`faq-${index}`}
                      className={`px-6 pb-6 transition-all duration-200 ease-in-out ${
                        openFaqIndex === index ? 'block' : 'hidden'
                      }`}
                      role="region"
                      aria-labelledby={`faq-${index}-button`}
                    >
                      <p className="text-muted-foreground leading-relaxed">{faq.answer}</p>
                    </div>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </section>

        {/* Emergency Section */}
        <section className="py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto">
              <Card className="border-0 shadow-large bg-gradient-to-br from-destructive/5 to-destructive/10 border-destructive/20">
                <CardHeader>
                  <CardTitle className="text-2xl flex items-center space-x-3 text-destructive">
                    <AlertTriangle className="w-6 h-6" />
                    <span>Emergenze Mediche</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <p className="text-foreground leading-relaxed">
                    Se stai vivendo un&apos;emergenza medica, chiama immediatamente il numero di emergenza del tuo paese:
                  </p>

                  <div className="grid gap-4 sm:grid-cols-2">
                    <div className="flex items-center space-x-3 p-4 bg-destructive/10 rounded-lg border border-destructive/20">
                      <Badge variant="destructive" className="text-xs font-bold">
                        118
                      </Badge>
                      <span className="text-foreground font-medium">Italia - Emergenza sanitaria</span>
                    </div>
                    <div className="flex items-center space-x-3 p-4 bg-destructive/10 rounded-lg border border-destructive/20">
                      <Badge variant="destructive" className="text-xs font-bold">
                        112
                      </Badge>
                      <span className="text-foreground font-medium">Numero unico di emergenza europeo</span>
                    </div>
                  </div>

                  <div className="p-4 bg-warning/10 rounded-lg border border-warning/20">
                    <div className="flex items-start space-x-3">
                      <Clock className="w-5 h-5 text-warning mt-0.5 flex-shrink-0" />
                      <div>
                        <h4 className="font-semibold text-warning mb-1">Situazioni non urgenti</h4>
                        <p className="text-sm text-muted-foreground">
                          Per situazioni non urgenti ma che richiedono attenzione medica, contatta il tuo medico di famiglia
                          o recati al pronto soccorso più vicino.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
