import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://www.scabbiasintomi.it'
  
  // Add all your main routes here
  const routes = [
    '',
    '/cos-e-la-scabbia',
    '/sintomi',
    '/cause',
    '/diagnosi',
    '/cura',
    '/prevenzione',
    '/contatti',
    '/blog',
    '/scabbia-checker',
    '/privacy',
    '/termini',
    '/cookie',
    '/faq',
  ].map((route) => ({
    url: `${baseUrl}${route}`,
    lastModified: new Date().toISOString().split('T')[0],
    changeFrequency: 'monthly' as const,
    priority: route === '' ? 1 : route === '/scabbia-checker' ? 0.9 : 0.8,
  }))

  return routes
}
