import { getAllPosts } from '@/lib/blog'

// Mock the Supabase client
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        order: jest.fn(() => ({
          data: [
            {
              id: 1,
              slug: 'test-post',
              title: 'Test Post',
              excerpt: 'Test excerpt',
              content: 'Test content',
              created_at: '2024-01-01T00:00:00Z'
            }
          ],
          error: null
        }))
      }))
    }))
  }))
}))

describe('Blog utilities', () => {
  describe('getAllPosts', () => {
    it('should return posts from Supabase', async () => {
      const posts = await getAllPosts()
      
      expect(posts).toHaveLength(1)
      expect(posts[0]).toEqual({
        id: 1,
        slug: 'test-post',
        title: 'Test Post',
        excerpt: 'Test excerpt',
        content: 'Test content',
        created_at: '2024-01-01T00:00:00Z'
      })
    })
  })
})
