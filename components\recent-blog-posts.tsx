import Link from "next/link";
import { getAllPosts } from "@/lib/blog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Clock, ArrowRight, BookOpen, Newspaper } from "lucide-react";

function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function calculateReadingTime(content: string) {
  const wordsPerMinute = 200;
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  const minutes = Math.ceil(words / wordsPerMinute);
  return `${minutes} min`;
}

export default async function RecentBlogPosts() {
  const posts = await getAllPosts();
  const recentPosts = posts.slice(0, 3); // Show only the 3 most recent posts

  if (recentPosts.length === 0) {
    return null; // Don't render the section if there are no posts
  }

  return (
    <section className="py-20 bg-background">
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center space-y-4 mb-16">
          <Badge variant="outline" className="mb-4">
            <Newspaper className="w-4 h-4 mr-2" />
            Ultimi Articoli
          </Badge>
          <h2 className="text-3xl sm:text-4xl font-bold text-foreground">
            Dal Nostro Blog
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Scopri gli ultimi articoli pubblicati sul nostro blog con informazioni 
            aggiornate e consigli medici sulla scabbia.
          </p>
        </div>

        {/* Recent Posts Grid */}
        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {recentPosts.map((post) => (
            <Link key={post.id} href={`/blog/${post.slug}`}>
              <Card className="group h-full hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  {/* Category Badge */}
                  <div className="flex items-center justify-between mb-4">
                    <Badge variant="secondary" className="text-xs">
                      Nuovo
                    </Badge>
                    <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-secondary-accent to-primary flex items-center justify-center shadow-medium">
                      <BookOpen className="w-5 h-5 text-white" />
                    </div>
                  </div>

                  {/* Title */}
                  <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors line-clamp-2 mb-3">
                    {post.title}
                  </h3>

                  {/* Excerpt */}
                  <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3 mb-4">
                    {post.excerpt}
                  </p>
                </CardHeader>

                <CardContent className="pt-0">
                  {/* Meta Information */}
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <div className="flex items-center">
                      <Calendar className="w-3 h-3 mr-1" />
                      {formatDate(post.created_at)}
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-3 h-3 mr-1" />
                      {calculateReadingTime(post.content)}
                    </div>
                  </div>

                  {/* Read More */}
                  <div className="mt-4 pt-4 border-t border-border/50">
                    <div className="flex items-center text-primary text-sm font-medium group-hover:text-primary-dark transition-colors">
                      Leggi articolo
                      <ArrowRight className="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* View All Blog Link */}
        <div className="text-center mt-12">
          <Link href="/blog">
            <Button size="lg" variant="outline" className="group">
              Vedi Tutti gli Articoli del Blog
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
