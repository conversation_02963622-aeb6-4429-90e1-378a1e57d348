import { render, screen } from '@testing-library/react'
import { EnvVarWarning } from '@/components/env-var-warning'

describe('Components', () => {
  describe('EnvVarWarning', () => {
    it('should render warning message', () => {
      render(<EnvVarWarning />)
      
      expect(screen.getByText('Supabase environment variables required')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /sign in/i })).toBeDisabled()
      expect(screen.getByRole('button', { name: /sign up/i })).toBeDisabled()
    })
  })
})
