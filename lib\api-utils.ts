import { toast } from "react-hot-toast";
import { useState } from "react";

// Define types for our API request/response
type ApiResponse = {
  answer: string;
  model: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
};

// Type for chat-based inputs
type ChatMessage = {
  role: 'user' | 'assistant' | 'system';
  content: string;
};

type ApiInput = { 
  messages: ChatMessage[];
  temperature?: number;
  max_tokens?: number;
};

// Rate limiting configuration
const RATE_LIMIT_REQUESTS = 10; // Max 10 requests
const RATE_LIMIT_INTERVAL = 60 * 1000; // Per minute

// Track API calls
let requestQueue: number[] = [];

// Clear old requests from queue
const cleanupQueue = () => {
  const now = Date.now();
  requestQueue = requestQueue.filter(timestamp => now - timestamp < RATE_LIMIT_INTERVAL);
};

// Check if request is allowed
const isRequestAllowed = (): boolean => {
  cleanupQueue();
  return requestQueue.length < RATE_LIMIT_REQUESTS;
};

// Add request to queue
const trackRequest = () => {
  requestQueue.push(Date.now());
};

export type ApiInputData = Record<string, unknown>;

export const callOpenAI = async (input: ApiInputData): Promise<{ data?: ApiResponse; error?: string }> => {
  // Transform the input to match the expected API format
  const apiInput: ApiInput = {
    messages: [
      { role: 'system', content: 'You are a helpful assistant.' },
      { role: 'user', content: JSON.stringify(input) },
    ],
    temperature: 0.7,
    max_tokens: 1000,
  };
  try {
    // Check rate limit
    if (!isRequestAllowed()) {
      return {
        error: "Hai effettuato troppe richieste di recente. Riprova tra un minuto.",
      };
    }

    trackRequest();

    const response = await fetch("/api/ask-ai", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ input: apiInput }),
    });

    const data = await response.json();

    if (!response.ok) {
      console.error("API Error:", data);
      
      // Handle specific error cases
      if (response.status === 429) {
        return { error: data.error || "Troppe richieste. Riprova più tardi." };
      } else if (response.status === 500) {
        return { error: "Errore del server. Riprova più tardi." };
      } else if (response.status === 504) {
        return { error: "Timeout della richiesta. Riprova." };
      }
      
      return { error: data.error || "Errore sconosciuto" };
    }

    return { data };
  } catch (error) {
    console.error("API Call Error:", error);
    return {
      error: "Connessione al server non riuscita. Verifica la tua connessione internet.",
    };
  }
};

// Utility to show error toast
const showError = (message: string) => {
  toast.error(message, {
    position: 'top-center',
    duration: 5000,
  });
};

// Hook for components to use the API
export const useOpenAI = (): {
  callAPI: (input: ApiInputData) => Promise<ApiResponse | null>;
  isLoading: boolean;
  error: string | null;
} => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const callAPI = async (input: ApiInputData): Promise<ApiResponse | null> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const { data, error } = await callOpenAI(input);
      
      if (error) {
        setError(error);
        showError(error);
        return null;
      }
      
      if (!data) {
        return null;
      }
      
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Errore sconosciuto';
      setError(errorMessage);
      showError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return { callAPI, isLoading, error };
};
