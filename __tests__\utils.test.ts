import { cn } from '@/lib/utils'

describe('Utils', () => {
  describe('cn function', () => {
    it('should merge class names correctly', () => {
      const result = cn('class1', 'class2')
      expect(result).toBe('class1 class2')
    })

    it('should handle conditional classes', () => {
      const result = cn('base', true && 'conditional', false && 'hidden')
      expect(result).toBe('base conditional')
    })

    it('should handle empty inputs', () => {
      const result = cn()
      expect(result).toBe('')
    })

    it('should handle undefined and null values', () => {
      const result = cn('base', undefined, null, 'valid')
      expect(result).toBe('base valid')
    })

    it('should merge tailwind classes correctly', () => {
      const result = cn('px-2 py-1', 'px-4')
      expect(result).toBe('py-1 px-4')
    })
  })

  describe('hasEnvVars', () => {
    const originalEnv = process.env

    beforeEach(() => {
      jest.resetModules()
      process.env = { ...originalEnv }
    })

    afterAll(() => {
      process.env = originalEnv
    })

    it('should return truthy when both env vars are set', () => {
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'test-url'
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-key'
      
      // Re-import to get updated env vars
      jest.isolateModules(() => {
        const { hasEnvVars } = require('@/lib/utils')
        expect(hasEnvVars).toBeTruthy()
      })
    })

    it('should return falsy when env vars are missing', () => {
      delete process.env.NEXT_PUBLIC_SUPABASE_URL
      delete process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      
      jest.isolateModules(() => {
        const { hasEnvVars } = require('@/lib/utils')
        expect(hasEnvVars).toBeFalsy()
      })
    })

    it('should return falsy when only one env var is set', () => {
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'test-url'
      delete process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      
      jest.isolateModules(() => {
        const { hasEnvVars } = require('@/lib/utils')
        expect(hasEnvVars).toBeFalsy()
      })
    })
  })
})
