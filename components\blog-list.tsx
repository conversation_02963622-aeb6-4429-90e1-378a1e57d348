import Link from "next/link";
import { getAllPosts } from "@/lib/blog";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Calendar, Clock, ArrowRight, BookOpen } from "lucide-react";

function formatDate(dateString: string) {
  const date = new Date(dateString);
  return date.toLocaleDateString('it-IT', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function calculateReadingTime(content: string) {
  const wordsPerMinute = 200;
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  const minutes = Math.ceil(words / wordsPerMinute);
  return `${minutes} min`;
}

export async function BlogList({ limit }: { limit?: number }) {
  const posts = await getAllPosts();
  const visible = limit ? posts.slice(0, limit) : posts;

  if (visible.length === 0) {
    return (
      <div className="text-center py-12">
        <BookOpen className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-muted-foreground">Nessun articolo disponibile.</p>
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {visible.map((post) => (
        <Link key={post.id} href={`/blog/${post.slug}`}>
          <Card className="group h-full hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
            <CardHeader className="pb-4">
              {/* Category Badge */}
              <div className="flex items-center justify-between mb-4">
                <Badge variant="secondary" className="text-xs">
                  Articolo
                </Badge>
                <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center shadow-medium">
                  <BookOpen className="w-5 h-5 text-white" />
                </div>
              </div>

              {/* Title */}
              <h3 className="text-xl font-bold text-foreground group-hover:text-primary transition-colors line-clamp-2 mb-3">
                {post.title}
              </h3>

              {/* Excerpt */}
              <p className="text-muted-foreground text-sm leading-relaxed line-clamp-3 mb-4">
                {post.excerpt}
              </p>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Meta Information */}
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <div className="flex items-center">
                  <Calendar className="w-3 h-3 mr-1" />
                  {formatDate(post.created_at)}
                </div>
                <div className="flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {calculateReadingTime(post.content)}
                </div>
              </div>

              {/* Read More */}
              <div className="mt-4 pt-4 border-t border-border/50">
                <div className="flex items-center text-primary text-sm font-medium group-hover:text-primary-dark transition-colors">
                  Leggi articolo
                  <ArrowRight className="w-3 h-3 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
}
