import { NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { marked } from "marked";

export async function POST(request: Request) {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const body = await request.json();
  const { title, slug, excerpt, content } = body;

  if (!title || !slug || !content) {
    return NextResponse.json({ error: "Missing fields" }, { status: 400 });
  }

  const html = marked.parse(content);

  const { error } = await supabase
    .from("posts")
    .insert({ title, slug, excerpt, content: html });

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  return NextResponse.json({ success: true });
}
