import { NextResponse } from "next/server";
import OpenAI from "openai";
import { scabiesOraclePrompt } from "@/lib/scabiesPrompt";

// Function to get OpenAI client (lazy initialization)
function getOpenAIClient() {
  if (!process.env.OPENAI_API_KEY) {
    throw new Error("OPENAI_API_KEY environment variable is not set");
  }

  return new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    timeout: 30000, // 30 seconds timeout
    maxRetries: 2,
  });
}

export async function POST(req: Request) {
  try {
    const { input } = await req.json();

    if (!input) {
      return NextResponse.json(
        { error: "Input mancante" },
        { status: 400 }
      );
    }

    // Verify payment status here if needed
    // You might want to check a database or session to confirm payment

    // Initialize OpenAI client only when needed
    const openai = getOpenAIClient();

    const model = process.env.AI_MODEL || "gpt-4.1-nano";
    const completion = await openai.chat.completions.create({
      model,
      messages: [
        { 
          role: "system", 
          content: scabiesOraclePrompt,
        },
        { 
          role: "user", 
          content: JSON.stringify(input) 
        },
      ],
      temperature: 0.7,
      max_tokens: 1000,
    });

    if (!completion.choices[0]?.message?.content) {
      throw new Error("Risposta vuota dal modello");
    }

    return NextResponse.json({
      answer: completion.choices[0].message.content,
      model,
      usage: completion.usage,
    });
  } catch (error) {
    console.error("Error in analyze-answers:", error);
    return NextResponse.json(
      { error: "Errore durante l'analisi delle risposte" },
      { status: 500 }
    );
  }
}
