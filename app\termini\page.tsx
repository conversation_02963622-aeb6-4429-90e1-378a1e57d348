import type { Metadata } from 'next'
import Navbar from '../components/Navbar'
import Footer from '../components/Footer'
import { Badge } from '@/components/ui/badge'
import { Card } from '@/components/ui/card'
import { FileText, AlertTriangle, Scale, Users, Calendar } from 'lucide-react'

export const metadata: Metadata = {
  title: 'Termini di Servizio | ScabbiaSintomi.it',
  description: 'Termini e condizioni di utilizzo di ScabbiaSintomi.it. Leggi le regole e le responsabilità per l\'uso del nostro servizio.',
  keywords: 'termini di servizio, condizioni utilizzo, disclaimer medico, responsabilità',
}

const termsHighlights = [
  {
    icon: FileText,
    title: 'Servizio Informativo',
    content: 'ScabbiaSintomi.it fornisce informazioni educative e strumenti di valutazione a scopo puramente informativo.'
  },
  {
    icon: AlertTriangle,
    title: 'Disclaimer Medico',
    content: 'Il servizio non sostituisce la consulenza medica professionale. Consulta sempre un medico per diagnosi e trattamenti.'
  },
  {
    icon: Scale,
    title: 'Responsabilità',
    content: 'L\'utilizzo del servizio è a tuo rischio. Non siamo responsabili per decisioni prese basandosi sulle nostre informazioni.'
  },
  {
    icon: Users,
    title: 'Uso Appropriato',
    content: 'Il servizio deve essere utilizzato in modo responsabile e in conformità con questi termini di servizio.'
  }
]

export default function TerminiPage() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                Termini e Condizioni
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                <span className="text-gradient">Termini di Servizio</span>
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Leggi attentamente i termini e le condizioni per l'utilizzo di ScabbiaSintomi.it
              </p>
            </div>
          </div>
        </section>

        {/* Terms Overview */}
        <section className="py-16">
          <div className="container-custom">
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
              {termsHighlights.map((item, index) => (
                <Card key={index} className="p-6 text-center border-0 shadow-medium bg-card/50 backdrop-blur-sm">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <item.icon className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="font-semibold text-foreground mb-3">{item.title}</h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">{item.content}</p>
                </Card>
              ))}
            </div>

            {/* Detailed Terms */}
            <div className="max-w-4xl mx-auto space-y-12">
              <Card className="p-8 border-0 shadow-medium bg-card/50 backdrop-blur-sm">
                <div className="space-y-8">
                  <div>
                    <h2 className="text-2xl font-bold text-foreground mb-4">1. Accettazione dei Termini</h2>
                    <p className="text-muted-foreground leading-relaxed">
                      Utilizzando ScabbiaSintomi.it, accetti integralmente questi termini di servizio. 
                      Se non accetti questi termini, ti preghiamo di non utilizzare il nostro servizio.
                    </p>
                  </div>

                  <div>
                    <h2 className="text-2xl font-bold text-foreground mb-4">2. Descrizione del Servizio</h2>
                    <p className="text-muted-foreground leading-relaxed mb-4">
                      ScabbiaSintomi.it è una piattaforma informativa che fornisce:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li>• Informazioni educative sulla scabbia</li>
                      <li>• Strumenti di valutazione dei sintomi basati su AI</li>
                      <li>• Contenuti informativi su prevenzione e trattamento</li>
                      <li>• Risorse per la comprensione della condizione</li>
                    </ul>
                  </div>

                  <div className="bg-warning/10 border border-warning/20 rounded-lg p-6">
                    <h2 className="text-2xl font-bold text-foreground mb-4 flex items-center gap-2">
                      <AlertTriangle className="w-6 h-6 text-warning" />
                      3. Importante Disclaimer Medico
                    </h2>
                    <div className="space-y-4 text-muted-foreground">
                      <p className="leading-relaxed">
                        <strong className="text-foreground">ATTENZIONE:</strong> Le informazioni fornite su questo sito 
                        sono esclusivamente a scopo educativo e informativo. Non costituiscono consulenza medica, 
                        diagnosi o trattamento.
                      </p>
                      <p className="leading-relaxed">
                        Il nostro strumento di valutazione dei sintomi utilizza algoritmi basati su criteri clinici 
                        standardizzati, ma <strong className="text-foreground">NON sostituisce in alcun modo</strong> 
                        la valutazione di un medico qualificato.
                      </p>
                      <p className="leading-relaxed">
                        <strong className="text-foreground">Consulta sempre un medico</strong> per qualsiasi problema 
                        di salute. In caso di emergenza medica, contatta immediatamente i servizi di emergenza.
                      </p>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-2xl font-bold text-foreground mb-4">4. Uso Appropriato del Servizio</h2>
                    <p className="text-muted-foreground leading-relaxed mb-4">
                      Ti impegni a utilizzare il servizio in modo responsabile e appropriato. È vietato:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li>• Utilizzare il servizio per scopi illegali o non autorizzati</li>
                      <li>• Tentare di compromettere la sicurezza del sito</li>
                      <li>• Fornire informazioni false o fuorvianti</li>
                      <li>• Utilizzare il servizio per sostituire la consulenza medica professionale</li>
                      <li>• Riprodurre o distribuire i contenuti senza autorizzazione</li>
                    </ul>
                  </div>

                  <div>
                    <h2 className="text-2xl font-bold text-foreground mb-4">5. Limitazione di Responsabilità</h2>
                    <p className="text-muted-foreground leading-relaxed mb-4">
                      ScabbiaSintomi.it e i suoi gestori non sono responsabili per:
                    </p>
                    <ul className="space-y-2 text-muted-foreground">
                      <li>• Decisioni mediche prese basandosi sulle informazioni del sito</li>
                      <li>• Ritardi o mancate diagnosi dovute all'uso del servizio</li>
                      <li>• Danni diretti o indiretti derivanti dall'uso del servizio</li>
                      <li>• Interruzioni temporanee del servizio</li>
                      <li>• Errori o imprecisioni nelle informazioni fornite</li>
                    </ul>
                  </div>

                  <div>
                    <h2 className="text-2xl font-bold text-foreground mb-4">6. Proprietà Intellettuale</h2>
                    <p className="text-muted-foreground leading-relaxed">
                      Tutti i contenuti del sito, inclusi testi, immagini, loghi e software, sono protetti 
                      da diritti d'autore e altri diritti di proprietà intellettuale. È vietata la riproduzione 
                      non autorizzata dei contenuti.
                    </p>
                  </div>

                  <div>
                    <h2 className="text-2xl font-bold text-foreground mb-4">7. Modifiche ai Termini</h2>
                    <p className="text-muted-foreground leading-relaxed">
                      Ci riserviamo il diritto di modificare questi termini in qualsiasi momento. 
                      Le modifiche saranno pubblicate su questa pagina e entreranno in vigore immediatamente. 
                      L'uso continuato del servizio costituisce accettazione delle modifiche.
                    </p>
                  </div>

                  <div>
                    <h2 className="text-2xl font-bold text-foreground mb-4">8. Legge Applicabile</h2>
                    <p className="text-muted-foreground leading-relaxed">
                      Questi termini sono regolati dalla legge italiana. Qualsiasi controversia sarà 
                      sottoposta alla giurisdizione esclusiva dei tribunali italiani.
                    </p>
                  </div>

                  <div>
                    <h2 className="text-2xl font-bold text-foreground mb-4">9. Contatti</h2>
                    <p className="text-muted-foreground leading-relaxed">
                      Per domande sui termini di servizio, contattaci all'indirizzo: 
                      <strong> <EMAIL></strong>
                    </p>
                  </div>

                  <div className="pt-6 border-t border-border">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="w-4 h-4" />
                      <span>Ultimo aggiornamento: {new Date().toLocaleDateString('it-IT')}</span>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  )
}
