import { Metadata } from 'next';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, AlertTriangle, Clock, Eye, MapPin, Thermometer, Moon, Baby, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

export const metadata: Metadata = {
  title: 'Sintomi della Scabbia | Come Riconoscerli - ScabbiaSintomi.it',
  description: 'Scopri i principali sintomi della scabbia, come riconoscerli e quando è necessario consultare un medico. Guida completa con informazioni dettagliate.',
  keywords: 'sintomi scabbia, prurito, eruzione cutanea, diagnosi, riconoscere scabbia',
};

const mainSymptoms = [
  {
    icon: AlertTriangle,
    title: 'Prurito intenso',
    description: 'Particolarmente fastidioso di notte o dopo un bagno caldo',
    severity: 'Alto'
  },
  {
    icon: Eye,
    title: 'Eruzione cutanea',
    description: 'Piccoli brufoletti rossi o vesciche, spesso in fila o a grappolo',
    severity: 'Alto'
  },
  {
    icon: MapPin,
    title: 'Linee sottili sulla pelle',
    description: 'Piccoli solchi grigiastri o color pelle, segno delle gallerie scavate dagli acari',
    severity: 'Medio'
  },
  {
    icon: Thermometer,
    title: 'Irritazione cutanea',
    description: 'Arrossamento e infiammazione nelle zone colpite',
    severity: 'Medio'
  }
];

const affectedAreas = [
  'Tra le dita delle mani e dei piedi',
  'Polsi e gomiti',
  'Ascelle',
  'Area genitale',
  'Pieghe del ginocchio',
  'Parte bassa dei glutei',
  'Parte laterali dei piedi'
];

const childrenAreas = [
  'Cuoio capelluto',
  'Viso',
  'Palmi delle mani',
  'Piante dei piedi'
];

export default function Sintomi() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                Riconoscimento Sintomi
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                <span className="text-gradient">Sintomi</span> della Scabbia
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Impara a riconoscere i segnali della scabbia per un intervento tempestivo
                e una diagnosi accurata.
              </p>
            </div>
          </div>
        </section>

        {/* Introduction Section */}
        <section className="py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto space-y-12">
              <Card className="border-0 bg-card/50 backdrop-blur-sm">
                <CardContent className="p-8">
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    I sintomi della scabbia possono comparire da 2 a 6 settimane dopo l&apos;infestazione iniziale.
                    Nelle persone che hanno già avuto la scabbia, i sintomi possono manifestarsi entro pochi giorni.
                  </p>
                </CardContent>
              </Card>

              {/* Main Symptoms */}
              <div className="space-y-8">
                <div className="text-center space-y-4">
                  <h2 className="text-3xl font-bold text-foreground">
                    Sintomi Principali
                  </h2>
                  <p className="text-muted-foreground">
                    I segnali più comuni che indicano la presenza di scabbia
                  </p>
                </div>

                <div className="grid gap-6 md:grid-cols-2">
                  {mainSymptoms.map((symptom, index) => (
                    <Card key={index} className="group hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between mb-4">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center shadow-medium">
                            <symptom.icon className="w-6 h-6 text-white" />
                          </div>
                          <Badge variant={symptom.severity === 'Alto' ? 'destructive' : 'secondary'}>
                            {symptom.severity}
                          </Badge>
                        </div>
                        <CardTitle className="text-xl text-foreground">
                          {symptom.title}
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-muted-foreground leading-relaxed">
                          {symptom.description}
                        </p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Affected Areas */}
              <div className="grid lg:grid-cols-2 gap-12 items-start">
                <Card className="border-0 bg-primary-light/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                      <MapPin className="w-6 h-6 text-primary" />
                      Zone più colpite
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid gap-3">
                      {affectedAreas.map((area, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-background/80 rounded-lg">
                          <div className="w-2 h-2 bg-primary rounded-full"></div>
                          <span className="text-foreground">{area}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-warning-light/50 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                      <Baby className="w-6 h-6 text-warning" />
                      Nei neonati e bambini piccoli
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-muted-foreground mb-4">
                      Nei bambini molto piccoli, la scabbia può colpire anche:
                    </p>
                    <div className="grid gap-3">
                      {childrenAreas.map((area, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-background/80 rounded-lg">
                          <div className="w-2 h-2 bg-warning rounded-full"></div>
                          <span className="text-foreground">{area}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Warning Section */}
              <Card className="border-l-4 border-l-destructive bg-destructive-light/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <AlertCircle className="w-6 h-6 text-destructive flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">
                        Attenzione: Sintomi gravi
                      </h3>
                      <p className="text-muted-foreground">
                        Se manifesti sintomi gravi come febbre, pus o gonfiore, consulta immediatamente un medico.
                        Questi potrebbero essere segni di un&apos;infezione secondaria che richiede trattamento antibiotico.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* CTA Section */}
              <div className="text-center space-y-6 py-8">
                <h3 className="text-2xl font-bold text-foreground">
                  Riconosci questi sintomi?
                </h3>
                <p className="text-muted-foreground">
                  Fai il nostro test AI gratuito per una valutazione immediata dei tuoi sintomi.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/scabbia-checker">
                    <Button size="lg" className="bg-gradient-to-r from-primary to-primary-dark">
                      Fai il Test Gratuito
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                  <Link href="/diagnosi">
                    <Button variant="outline" size="lg">
                      Scopri la Diagnosi
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
