#!/bin/bash
set -e

# Update system packages
sudo apt-get update

# Install Node.js 20 (LTS) if not already installed
if ! command -v node &> /dev/null || [[ $(node -v | cut -d'.' -f1 | cut -d'v' -f2) -lt 18 ]]; then
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt-get install -y nodejs
fi

# Verify Node.js and npm versions
echo "Node.js version: $(node -v)"
echo "npm version: $(npm -v)"

# Navigate to workspace
cd /mnt/persist/workspace

# Install project dependencies
npm install

# Install testing dependencies following Next.js best practices
npm install --save-dev jest jest-environment-jsdom @testing-library/react @testing-library/jest-dom @testing-library/user-event

# Create Jest configuration file with correct property name
cat > jest.config.js << 'EOF'
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)
EOF

# Create Jest setup file
cat > jest.setup.js << 'EOF'
import '@testing-library/jest-dom'
EOF

# Create a comprehensive test suite for the utils
mkdir -p __tests__
cat > __tests__/utils.test.ts << 'EOF'
import { cn } from '@/lib/utils'

describe('Utils', () => {
  describe('cn function', () => {
    it('should merge class names correctly', () => {
      const result = cn('class1', 'class2')
      expect(result).toBe('class1 class2')
    })

    it('should handle conditional classes', () => {
      const result = cn('base', true && 'conditional', false && 'hidden')
      expect(result).toBe('base conditional')
    })

    it('should handle empty inputs', () => {
      const result = cn()
      expect(result).toBe('')
    })

    it('should handle undefined and null values', () => {
      const result = cn('base', undefined, null, 'valid')
      expect(result).toBe('base valid')
    })

    it('should merge tailwind classes correctly', () => {
      const result = cn('px-2 py-1', 'px-4')
      expect(result).toBe('py-1 px-4')
    })
  })

  describe('hasEnvVars', () => {
    const originalEnv = process.env

    beforeEach(() => {
      jest.resetModules()
      process.env = { ...originalEnv }
    })

    afterAll(() => {
      process.env = originalEnv
    })

    it('should return truthy when both env vars are set', () => {
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'test-url'
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-key'
      
      // Re-import to get updated env vars
      jest.isolateModules(() => {
        const { hasEnvVars } = require('@/lib/utils')
        expect(hasEnvVars).toBeTruthy()
      })
    })

    it('should return falsy when env vars are missing', () => {
      delete process.env.NEXT_PUBLIC_SUPABASE_URL
      delete process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      
      jest.isolateModules(() => {
        const { hasEnvVars } = require('@/lib/utils')
        expect(hasEnvVars).toBeFalsy()
      })
    })

    it('should return falsy when only one env var is set', () => {
      process.env.NEXT_PUBLIC_SUPABASE_URL = 'test-url'
      delete process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
      
      jest.isolateModules(() => {
        const { hasEnvVars } = require('@/lib/utils')
        expect(hasEnvVars).toBeFalsy()
      })
    })
  })
})
EOF

# Create a test for the blog utilities
cat > __tests__/blog.test.ts << 'EOF'
import { getAllPosts } from '@/lib/blog'

// Mock the Supabase client
jest.mock('@/lib/supabase/server', () => ({
  createClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        order: jest.fn(() => ({
          data: [
            {
              id: 1,
              slug: 'test-post',
              title: 'Test Post',
              excerpt: 'Test excerpt',
              content: 'Test content',
              created_at: '2024-01-01T00:00:00Z'
            }
          ],
          error: null
        }))
      }))
    }))
  }))
}))

describe('Blog utilities', () => {
  describe('getAllPosts', () => {
    it('should return posts from Supabase', async () => {
      const posts = await getAllPosts()
      
      expect(posts).toHaveLength(1)
      expect(posts[0]).toEqual({
        id: 1,
        slug: 'test-post',
        title: 'Test Post',
        excerpt: 'Test excerpt',
        content: 'Test content',
        created_at: '2024-01-01T00:00:00Z'
      })
    })
  })
})
EOF

# Create a test for React components
cat > __tests__/components.test.tsx << 'EOF'
import { render, screen } from '@testing-library/react'
import { EnvVarWarning } from '@/components/env-var-warning'

describe('Components', () => {
  describe('EnvVarWarning', () => {
    it('should render warning message', () => {
      render(<EnvVarWarning />)
      
      expect(screen.getByText('Supabase environment variables required')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /sign in/i })).toBeDisabled()
      expect(screen.getByRole('button', { name: /sign up/i })).toBeDisabled()
    })
  })
})
EOF

# Add test script to package.json
npm pkg set scripts.test="jest"
npm pkg set scripts.test:watch="jest --watch"
npm pkg set scripts.test:coverage="jest --coverage"

# Create .env.test file for testing environment
cat > .env.test << 'EOF'
# Test environment variables
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=test-anon-key
STRIPE_SECRET_KEY=sk_test_fake_key
STRIPE_PRODUCT_ID=prod_test
AI_MODEL=gpt-4-test
OPENAI_API_KEY=test-openai-key
EOF

echo "Testing environment setup completed successfully!"
echo "Available test commands:"
echo "- npm test: Run all tests"
echo "- npm run test:watch: Run tests in watch mode"
echo "- npm run test:coverage: Run tests with coverage report"