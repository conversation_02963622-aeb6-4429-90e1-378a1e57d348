import { NextResponse } from "next/server";
import Stripe from "stripe";
import { headers } from 'next/headers';

// Function to get Stripe client (lazy initialization)
function getStripeClient() {
  if (!process.env.STRIPE_SECRET_KEY) {
    throw new Error("STRIPE_SECRET_KEY environment variable is not set");
  }

  return new Stripe(process.env.STRIPE_SECRET_KEY, {
    apiVersion: "2025-05-28.basil",
    typescript: true,
  });
}

export async function POST(request: Request) {
  try {
    const body = await request.text();
    const headersList = await headers();
    const signature = headersList.get('stripe-signature') || '';

    let event: Stripe.Event;

    try {
      // Initialize Stripe client only when needed
      const stripe = getStripeClient();
      event = stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET || ''
      );
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    // Handle the checkout.session.completed event
    if (event.type === 'checkout.session.completed') {
      const session = event.data.object as Stripe.Checkout.Session;
      
      // Here you would typically:
      // 1. Verify the payment was successful
      // 2. Update your database to mark the payment as complete
      // 3. Grant access to the purchased service
      
      console.log('Payment successful for session:', session.id);
      
      // You can access the metadata you set earlier
      const service = session.metadata?.service;
      console.log('Service:', service);
      
      // TODO: Update your database or perform other actions
      
      return NextResponse.json({ received: true });
    }

    // Handle other event types as needed
    console.log(`Unhandled event type: ${event.type}`);
    return NextResponse.json({ received: true });
    
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}
