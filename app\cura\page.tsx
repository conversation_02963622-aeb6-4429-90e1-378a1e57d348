import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, Pill, Stethoscope, Clock, AlertTriangle, CheckCircle, Thermometer, Droplets } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

export const metadata: Metadata = {
  title: 'Cura della Scabbia | Trattamenti Efficaci - ScabbiaSintomi.it',
  description: 'Scopri i trattamenti disponibili per la scabbia, come applicarli correttamente e come prevenire la reinfestazione. Guida completa per una guarigione efficace.',
  keywords: 'cura scabbia, trattamento, permetrina, ivermectina, farmaci, guarigione',
};

const treatments = [
  {
    name: 'Permetrina 5% crema',
    icon: Droplets,
    type: 'Topico',
    effectiveness: 'Alto',
    usage: [
      'Applicare su tutto il corpo dal collo in giù',
      'Lasciare in posa per 8-14 ore',
      'Risciacquare abbondantemente',
      'Ripetere dopo 7 giorni'
    ],
    sideEffects: [
      'Lieve bruciore o prurito',
      'Formicolio',
      'Arrossamento della pelle'
    ]
  },
  {
    name: 'Ivermectina orale',
    icon: Pill,
    type: 'Orale',
    effectiveness: 'Alto',
    usage: [
      'Assunta per bocca in dose unica',
      'Dosaggio in base al peso',
      'Ripetere dopo 7-14 giorni'
    ],
    sideEffects: [
      'Mal di testa',
      'Vertigini',
      'Dolori muscolari',
      'Nausea'
    ]
  }
];

const otherTreatments = [
  {
    name: 'Crotamitone',
    description: 'Crema o lozione applicata per 2-5 giorni',
    icon: Droplets
  },
  {
    name: 'Benzil benzoato',
    description: 'Applicazione notturna per 3 giorni',
    icon: Droplets
  },
  {
    name: 'Zolfo precipitato',
    description: 'Per neonati, donne in gravidanza o allattamento',
    icon: Droplets
  }
];

export default function Cura() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                Trattamento e Cura
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                <span className="text-gradient">Cura</span> della Scabbia
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Scopri i trattamenti più efficaci e come applicarli correttamente
                per una guarigione completa e duratura.
              </p>
            </div>
          </div>
        </section>

        {/* Introduction Section */}
        <section className="py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto space-y-12">
              <Card className="border-0 bg-card/50 backdrop-blur-sm">
                <CardContent className="p-8">
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    La scabbia richiede un trattamento specifico per eliminare gli acari e le loro uova. Il trattamento è solitamente
                    topico (applicato sulla pelle) e deve essere eseguito correttamente per garantire l&apos;eradicazione completa degli acari.
                  </p>
                </CardContent>
              </Card>

              {/* Main Treatments */}
              <div className="space-y-8">
                <div className="text-center space-y-4">
                  <h2 className="text-3xl font-bold text-foreground">
                    Trattamenti farmacologici
                  </h2>
                  <p className="text-muted-foreground">
                    I farmaci più efficaci per eliminare gli acari della scabbia
                  </p>
                </div>

                <div className="grid gap-8">
                  {treatments.map((treatment, index) => (
                    <Card key={index} className="group hover:shadow-large transition-all duration-300 border-0 bg-card/50 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between mb-4">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center shadow-medium">
                            <treatment.icon className="w-6 h-6 text-white" />
                          </div>
                          <div className="flex gap-2">
                            <Badge variant="outline">{treatment.type}</Badge>
                            <Badge variant="default">Efficacia {treatment.effectiveness}</Badge>
                          </div>
                        </div>
                        <CardTitle className="text-2xl text-foreground">
                          {treatment.name}
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="grid md:grid-cols-2 gap-6">
                          <div className="space-y-4">
                            <h4 className="font-semibold text-foreground flex items-center gap-2">
                              <CheckCircle className="w-4 h-4 text-success" />
                              Come si usa:
                            </h4>
                            <div className="space-y-2">
                              {treatment.usage.map((step, idx) => (
                                <div key={idx} className="flex items-start gap-3 p-3 bg-background/50 rounded-lg">
                                  <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                                    <span className="text-xs font-medium text-primary">{idx + 1}</span>
                                  </div>
                                  <span className="text-muted-foreground text-sm">{step}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                          <div className="space-y-4">
                            <h4 className="font-semibold text-foreground flex items-center gap-2">
                              <AlertTriangle className="w-4 h-4 text-warning" />
                              Effetti collaterali:
                            </h4>
                            <div className="space-y-2">
                              {treatment.sideEffects.map((effect, idx) => (
                                <div key={idx} className="flex items-center gap-3 p-3 bg-warning-light/20 rounded-lg">
                                  <div className="w-2 h-2 bg-warning rounded-full"></div>
                                  <span className="text-muted-foreground text-sm">{effect}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Other Treatments */}
                <Card className="border-0 bg-secondary/20 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                      <Stethoscope className="w-6 h-6 text-secondary-accent" />
                      Altri trattamenti
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-3 gap-6">
                      {otherTreatments.map((treatment, index) => (
                        <Card key={index} className="border-0 bg-background/80">
                          <CardContent className="p-4 space-y-3">
                            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-secondary-accent to-primary flex items-center justify-center">
                              <treatment.icon className="w-5 h-5 text-white" />
                            </div>
                            <h4 className="font-semibold text-foreground">{treatment.name}</h4>
                            <p className="text-sm text-muted-foreground">{treatment.description}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Treatment Guidelines */}
              <Card className="border-0 bg-primary-light/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                    <CheckCircle className="w-6 h-6 text-primary" />
                    Cosa fare durante il trattamento
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-8">
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-foreground mb-4">Per la persona infetta:</h3>
                      <div className="space-y-3">
                        {[
                          'Applicare il trattamento su tutto il corpo, non solo sulle aree con eruzione cutanea',
                          'Tagliare le unghie corte e pulirle bene',
                          'Indossare indumenti puliti dopo il trattamento',
                          'Evitare di grattarsi per prevenire infezioni'
                        ].map((instruction, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 bg-background/80 rounded-lg">
                            <CheckCircle className="w-5 h-5 text-success flex-shrink-0 mt-0.5" />
                            <span className="text-foreground text-sm">{instruction}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold text-foreground mb-4">Per l&apos;ambiente:</h3>
                      <div className="space-y-3">
                        {[
                          'Lavare lenzuola, asciugamani e indumenti a 60°C',
                          'Sigillare in sacchetti di plastica per 72 ore gli oggetti non lavabili',
                          'Aspirare tappeti, materassi e divani',
                          'Trattare tutti i membri della famiglia contemporaneamente'
                        ].map((instruction, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 bg-background/80 rounded-lg">
                            <CheckCircle className="w-5 h-5 text-success flex-shrink-0 mt-0.5" />
                            <span className="text-foreground text-sm">{instruction}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Itch Management */}
              <div className="space-y-8">
                <div className="text-center space-y-4">
                  <h2 className="text-3xl font-bold text-foreground">
                    Gestione del prurito
                  </h2>
                  <p className="text-muted-foreground max-w-2xl mx-auto">
                    Il prurito può persistere per 2-4 settimane dopo il trattamento, anche se gli acari sono stati eliminati.
                    Ecco alcuni rimedi per alleviare il fastidio:
                  </p>
                </div>

                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    {
                      title: 'Antistaminici orali',
                      description: 'Come la cetirizina o la difenidramina per ridurre il prurito',
                      icon: Pill
                    },
                    {
                      title: 'Creme idratanti',
                      description: 'Applicare creme emollienti per lenire la pelle irritata',
                      icon: Droplets
                    },
                    {
                      title: 'Impacchi freddi',
                      description: 'Applicare panni freddi sulle zone pruriginose',
                      icon: Thermometer
                    },
                    {
                      title: 'Bagni con farina d\'avena',
                      description: 'Aiutano a lenire il prurito e l\'infiammazione',
                      icon: Droplets
                    },
                    {
                      title: 'Cortisonici topici',
                      description: 'Per ridurre l\'infiammazione nelle aree più irritate',
                      icon: Pill
                    },
                    {
                      title: 'Evitare il caldo eccessivo',
                      description: 'Il calore può peggiorare il prurito',
                      icon: Thermometer
                    },
                  ].map((item, index) => (
                    <Card key={index} className="group hover:shadow-large transition-all duration-300 hover:-translate-y-1 border-0 bg-card/50 backdrop-blur-sm">
                      <CardContent className="p-6 space-y-4">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-success to-success-light flex items-center justify-center shadow-medium">
                          <item.icon className="w-6 h-6 text-white" />
                        </div>
                        <h3 className="font-semibold text-foreground">{item.title}</h3>
                        <p className="text-sm text-muted-foreground leading-relaxed">{item.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>

              {/* Warning Section */}
              <Card className="border-l-4 border-l-warning bg-warning-light/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <Clock className="w-6 h-6 text-warning flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">
                        Quando consultare nuovamente il medico
                      </h3>
                      <p className="text-muted-foreground">
                        Contatta il tuo medico se i sintomi persistono dopo 2-4 settimane dal trattamento,
                        se peggiorano o se noti segni di infezione (come pus, aumento del rossore o febbre).
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* CTA Section */}
              <div className="text-center space-y-6 py-8">
                <h3 className="text-2xl font-bold text-foreground">
                  Hai bisogno di una diagnosi?
                </h3>
                <p className="text-muted-foreground">
                  Fai il nostro test AI per valutare i tuoi sintomi e ricevere consigli personalizzati.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/scabbia-checker">
                    <Button size="lg" className="bg-gradient-to-r from-primary to-primary-dark">
                      Fai il Test Gratuito
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                  <Link href="/prevenzione">
                    <Button variant="outline" size="lg">
                      Scopri la Prevenzione
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
