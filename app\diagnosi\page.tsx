import { Metadata } from 'next';
import Link from 'next/link';
import { ArrowRight, Stethoscope, Search, Microscope, FileText, AlertCircle, Clock, CheckCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

export const metadata: Metadata = {
  title: 'Diagnosi della Scabbia | Come si Riconosce - ScabbiaSintomi.it',
  description: 'Scopri come viene diagnosticata la scabbia, quali esami sono disponibili e quando consultare un medico. Guida completa per una diagnosi accurata.',
  keywords: 'diagnosi scabbia, esami, test, dermatoscopio, raschiamento cutaneo, visita medica',
};

const diagnosticSteps = [
  {
    step: 1,
    title: 'Anamnesi medica',
    description: 'Il medico inizierà con domande su sintomi, storia medica e possibili esposizioni a persone con sintomi simili.',
    icon: FileText
  },
  {
    step: 2,
    title: 'Esame fisico',
    description: 'Verrà esaminata la pelle alla ricerca di segni caratteristici come gallerie sottocutanee, pomfi o vescicole.',
    icon: Search,
    details: [
      'Gallerie sottocutanee (piccoli solchi grigiastri)',
      'Pomfi o vescicole nelle zone tipiche',
      'Segni di grattamento'
    ]
  },
  {
    step: 3,
    title: 'Test di conferma',
    description: 'In caso di dubbio, il medico potrebbe eseguire test specifici per confermare la diagnosi.',
    icon: Microscope
  }
];

const confirmationTests = [
  {
    name: 'Test dell\'inchiostro',
    description: 'Si passa un pennarello sulla pelle e poi si pulisce con alcol. Le gallerie trattengono l\'inchiostro e diventano visibili.',
    icon: Search
  },
  {
    name: 'Raschiamento cutaneo',
    description: 'Si preleva un piccolo campione di pelle per esaminarlo al microscopio alla ricerca di acari, uova o feci.',
    icon: Microscope
  },
  {
    name: 'Dermatoscopia',
    description: 'Utilizzo di un dermatoscopio per ingrandire la pelle e identificare le gallerie e gli acari.',
    icon: Search
  },
  {
    name: 'Biopsia cutanea',
    description: 'In rari casi, può essere necessaria una piccola biopsia della pelle per l\'analisi in laboratorio.',
    icon: Microscope
  }
];

const differentialDiagnosis = [
  { condition: 'Dermatite atopica', description: 'Infiammazione cronica della pelle con prurito intenso' },
  { condition: 'Orticaria', description: 'Pomfi pruriginosi che possono assomigliare alle lesioni della scabbia' },
  { condition: 'Psoriasi', description: 'Macchie rosse ricoperte di squame biancastre' },
  { condition: 'Pidocchi del corpo', description: 'Anche loro causano prurito intenso, ma si trovano sui vestiti' },
  { condition: 'Reazione allergica', description: 'Può causare eruzioni cutanee e prurito' },
  { condition: 'Eczema disidrosico', description: 'Piccole vesciche sui palmi delle mani e piante dei piedi' }
];

const visitExpectations = [
  'Il medico esaminerà attentamente la tua pelle, prestando particolare attenzione alle zone tra le dita, i polsi, i gomiti, le ascelle e l\'area genitale.',
  'Potrebbe utilizzare una lente d\'ingrandimento per cercare i segni caratteristici della scabbia.',
  'Se necessario, preleverà un piccolo campione di pelle per l\'analisi al microscopio.',
  'Una volta confermata la diagnosi, ti verrà prescritto il trattamento appropriato e ti verranno date istruzioni su come disinfestare l\'ambiente domestico.'
];

export default function Diagnosi() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-background">
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-light via-background to-secondary/20 py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto text-center space-y-6">
              <Badge variant="outline" className="mb-4">
                Diagnosi Medica
              </Badge>
              <h1 className="text-4xl sm:text-5xl font-bold text-foreground">
                <span className="text-gradient">Diagnosi</span> della Scabbia
              </h1>
              <p className="text-xl text-muted-foreground leading-relaxed">
                Scopri come viene diagnosticata la scabbia e cosa aspettarti
                durante la visita medica.
              </p>
            </div>
          </div>
        </section>

        {/* Introduction Section */}
        <section className="py-20">
          <div className="container-custom">
            <div className="max-w-4xl mx-auto space-y-12">
              <Card className="border-0 bg-card/50 backdrop-blur-sm">
                <CardContent className="p-8">
                  <p className="text-lg text-muted-foreground leading-relaxed">
                    La diagnosi di scabbia viene solitamente effettuata attraverso un esame fisico e la valutazione dei sintomi.
                    In alcuni casi, potrebbero essere necessari esami più approfonditi per confermare la presenza degli acari o delle loro uova.
                  </p>
                </CardContent>
              </Card>

              {/* Diagnostic Process */}
              <div className="space-y-8">
                <div className="text-center space-y-4">
                  <h2 className="text-3xl font-bold text-foreground">
                    Come viene diagnosticata la scabbia
                  </h2>
                  <p className="text-muted-foreground">
                    Il processo diagnostico segue passaggi specifici per una diagnosi accurata
                  </p>
                </div>

                <div className="space-y-8">
                  {diagnosticSteps.map((step, index) => (
                    <Card key={index} className="group hover:shadow-large transition-all duration-300 border-0 bg-card/50 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="flex items-center gap-4 mb-4">
                          <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-primary to-primary-dark flex items-center justify-center shadow-medium">
                            <step.icon className="w-6 h-6 text-white" />
                          </div>
                          <div className="flex items-center gap-3">
                            <Badge variant="outline">Passo {step.step}</Badge>
                            <CardTitle className="text-xl text-foreground">
                              {step.title}
                            </CardTitle>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <p className="text-muted-foreground leading-relaxed">
                          {step.description}
                        </p>
                        {step.details && (
                          <div className="space-y-2">
                            <h4 className="font-semibold text-foreground">Segni caratteristici:</h4>
                            <div className="grid gap-2">
                              {step.details.map((detail, idx) => (
                                <div key={idx} className="flex items-center gap-3 p-3 bg-background/50 rounded-lg">
                                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                                  <span className="text-foreground text-sm">{detail}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>

                {/* Confirmation Tests */}
                <Card className="border-0 bg-secondary/20 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                      <Microscope className="w-6 h-6 text-secondary-accent" />
                      Test di conferma
                    </CardTitle>
                    <p className="text-muted-foreground">
                      In caso di dubbio, il medico potrebbe eseguire uno dei seguenti test:
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="grid md:grid-cols-2 gap-6">
                      {confirmationTests.map((test, index) => (
                        <Card key={index} className="border-0 bg-background/80">
                          <CardContent className="p-6 space-y-4">
                            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-secondary-accent to-primary flex items-center justify-center">
                              <test.icon className="w-5 h-5 text-white" />
                            </div>
                            <h4 className="font-semibold text-foreground">{test.name}</h4>
                            <p className="text-sm text-muted-foreground leading-relaxed">{test.description}</p>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Differential Diagnosis */}
              <Card className="border-0 bg-warning-light/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                    <AlertCircle className="w-6 h-6 text-warning" />
                    Diagnosi differenziale
                  </CardTitle>
                  <p className="text-muted-foreground">
                    Alcune condizioni possono essere confuse con la scabbia a causa di sintomi simili:
                  </p>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-4">
                    {differentialDiagnosis.map((item, index) => (
                      <Card key={index} className="border-0 bg-background/80">
                        <CardContent className="p-4 space-y-2">
                          <h4 className="font-semibold text-foreground">{item.condition}</h4>
                          <p className="text-sm text-muted-foreground">{item.description}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* When to See a Doctor */}
              <Card className="border-l-4 border-l-warning bg-warning-light/50 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-start gap-4">
                    <Clock className="w-6 h-6 text-warning flex-shrink-0 mt-1" />
                    <div>
                      <h3 className="font-semibold text-foreground mb-2">
                        Quando consultare un medico
                      </h3>
                      <p className="text-muted-foreground">
                        Rivolgiti a un medico se avverti prurito intenso che peggiora di notte, specialmente se noti un&apos;eruzione cutanea o se altre persone con cui sei stato a stretto contatto manifestano sintomi simili.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* What to Expect */}
              <Card className="border-0 bg-success-light/50 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="text-2xl text-foreground flex items-center gap-3">
                    <Stethoscope className="w-6 h-6 text-success" />
                    Cosa aspettarsi durante la visita
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {visitExpectations.map((expectation, index) => (
                      <div key={index} className="flex items-start gap-3 p-4 bg-background/80 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-success flex-shrink-0 mt-0.5" />
                        <span className="text-foreground leading-relaxed">{expectation}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* CTA Section */}
              <div className="text-center space-y-6 py-8">
                <h3 className="text-2xl font-bold text-foreground">
                  Hai sintomi sospetti?
                </h3>
                <p className="text-muted-foreground">
                  Fai il nostro test AI per una valutazione preliminare dei tuoi sintomi.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link href="/scabbia-checker">
                    <Button size="lg" className="bg-gradient-to-r from-primary to-primary-dark">
                      Fai il Test Gratuito
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </Link>
                  <Link href="/cura">
                    <Button variant="outline" size="lg">
                      Scopri le Cure
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
