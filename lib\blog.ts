import { createClient } from "@/lib/supabase/server";

export type Post = {
  id: number;
  slug: string;
  title: string;
  excerpt: string;
  content: string;
  created_at: string;
};

export async function getAllPosts(): Promise<Post[]> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("posts")
    .select("id, slug, title, excerpt, content, created_at")
    .order("created_at", { ascending: false });
  if (error) {
    if (error.code === "42P01") {
      console.error("Table 'posts' does not exist in Supabase");
      return [];
    }
    throw error;
  }
  return data as Post[];
}

export async function getPostBySlug(slug: string): Promise<Post | null> {
  const supabase = await createClient();
  const { data, error } = await supabase
    .from("posts")
    .select("id, slug, title, excerpt, content, created_at")
    .eq("slug", slug)
    .single();
  if (error) {
    if (error.code === "42P01") {
      console.error("Table 'posts' does not exist in Supabase");
      return null;
    }
    throw error;
  }
  return (data as Post) || null;
}
