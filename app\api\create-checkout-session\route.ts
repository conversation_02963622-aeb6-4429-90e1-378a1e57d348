import { NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";

// Function to get Stripe client (lazy initialization)
function getStripeClient() {
  if (!process.env.STRIPE_SECRET_KEY) {
    throw new Error("STRIPE_SECRET_KEY environment variable is not set");
  }

  return new Stripe(process.env.STRIPE_SECRET_KEY, {
    apiVersion: "2025-05-28.basil",
    typescript: true,
  });
}

export async function POST(request: Request) {
  if (!process.env.STRIPE_SECRET_KEY) {
    return NextResponse.json({ error: "Stripe non configurato" }, { status: 500 });
  }

  if (!process.env.STRIPE_PRODUCT_ID) {
    return NextResponse.json({ error: "Prodotto Stripe non configurato" }, { status: 500 });
  }

  try {
    // Initialize Stripe client only when needed
    const stripe = getStripeClient();
    const { return_url } = await request.json();
    const origin = request.headers.get("origin") || "";
    const successUrl = new URL(return_url || `${origin}/scabbia-checker/success`);
    
    // Add session_id as a query parameter to the success URL
    successUrl.searchParams.set("session_id", "{CHECKOUT_SESSION_ID}");
    successUrl.searchParams.set("payment_success", "true");

    const session = await stripe.checkout.sessions.create({
      mode: "payment",
      payment_method_types: ["card"],
      line_items: [
        {
          price_data: {
            currency: "eur",
            product: process.env.STRIPE_PRODUCT_ID,
            unit_amount: 100, // 1.00 EUR
          },
          quantity: 1,
        },
      ],
      metadata: {
        // Store any additional metadata
        service: "scabbia-checker",
        timestamp: new Date().toISOString(),
      },
      payment_intent_data: {
        metadata: {
          // This will be included in the webhook
          service: "scabbia-checker",
        },
      },
      customer_creation: "if_required",
      submit_type: "pay",
      allow_promotion_codes: false,
      billing_address_collection: "auto",
      locale: "it",
      success_url: successUrl.toString(),
      cancel_url: `${origin}/scabbia-checker/cancel`,
      expires_at: Math.floor(Date.now() / 1000) + 30 * 60, // 30 minutes from now
    });

    return NextResponse.json({ 
      url: session.url,
      sessionId: session.id 
    });
  } catch (error) {
    console.error("Error creating checkout session:", error);
    return NextResponse.json(
      { error: "Errore durante la creazione della sessione di pagamento" },
      { status: 500 }
    );
  }
}
