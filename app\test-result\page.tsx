'use client';

import { ResultVisualization } from '@/components/ui/result-visualization';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

// Sample data for testing the enhanced result presentation
const sampleReport = {
  probability: 75,
  summary: "In base alle tue risposte potresti avere la scabbia. I sintomi riportati sono compatibili con una possibile infezione da Sarcoptes scabiei. Si raccomanda una valutazione medica urgente.",
  details: "Hai risposto \"Sì\" a 6 domande su 10, in particolare ad alcune tipiche della scabbia come prurito notturno intenso, eruzioni cutanee nelle zone caratteristiche e possibili contatti a rischio.",
  analysis: JSON.stringify({
    "probability_percent": 75,
    "risk_level": "Alto",
    "iacs_level": "Clinical",
    "rationale": [
      "Prurito intenso che peggiora durante la notte, sintomo caratteristico della scabbia",
      "Presenza di eruzioni cutanee in localizzazioni tipiche (polsi, spazi interdigitali)",
      "Storia di possibili contatti con persone affette da scabbia",
      "Durata dei sintomi compatibile con il periodo di incubazione",
      "Assenza di miglioramento con trattamenti topici generici"
    ],
    "next_steps": "Si raccomanda urgentemente una visita dermatologica per conferma diagnostica. Nel frattempo, evitare contatti stretti, lavare biancheria e vestiti ad alta temperatura, e considerare l'isolamento preventivo dei materiali tessili.",
    "disclaimer": "Questa valutazione è puramente informativa e non sostituisce il parere medico professionale. Consultare sempre un dermatologo per diagnosi e trattamento definitivi."
  })
};

const sampleReportLow = {
  probability: 15,
  summary: "Dalle tue risposte sembra improbabile che tu abbia la scabbia. I sintomi riportati potrebbero essere dovuti ad altre condizioni dermatologiche meno specifiche.",
  details: "Solo 2 risposte su 10 indicano sintomi compatibili con la scabbia. La distribuzione e le caratteristiche dei sintomi suggeriscono altre possibili cause.",
  analysis: JSON.stringify({
    "probability_percent": 15,
    "risk_level": "Basso",
    "iacs_level": "Suspected",
    "rationale": [
      "Prurito presente ma non specificamente notturno",
      "Eruzioni cutanee in zone non tipiche della scabbia",
      "Nessuna storia di contatti a rischio documentati",
      "Sintomi potrebbero essere compatibili con dermatite da contatto o eczema",
      "Assenza di segni patognomonici come cunicoli visibili"
    ],
    "next_steps": "Monitorare l'evoluzione dei sintomi. Se persistono o peggiorano, consultare un medico di base o dermatologo per una valutazione più approfondita e diagnosi differenziale.",
    "disclaimer": "Questa valutazione è puramente informativa e non sostituisce il parere medico professionale. Consultare sempre un medico per diagnosi e trattamento definitivi."
  })
};

export default function TestResultPage() {
  return (
    <>
      <Navbar />
      <main className="min-h-screen bg-gradient-to-br from-primary/5 via-background to-secondary/5">
        <div className="container-custom py-12">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Test della Nuova Presentazione Risultati
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Anteprima del design professionale e moderno per i risultati del test scabbia
            </p>
          </div>

          <div className="space-y-16">
            {/* High Risk Result */}
            <div>
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-destructive mb-2">Esempio: Rischio Alto</h2>
                <p className="text-muted-foreground">Risultato con alta probabilità di scabbia</p>
              </div>
              <ResultVisualization
                probability={sampleReport.probability}
                summary={sampleReport.summary}
                details={sampleReport.details}
                analysis={sampleReport.analysis}
              />
            </div>

            {/* Low Risk Result */}
            <div>
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-success mb-2">Esempio: Rischio Basso</h2>
                <p className="text-muted-foreground">Risultato con bassa probabilità di scabbia</p>
              </div>
              <ResultVisualization
                probability={sampleReportLow.probability}
                summary={sampleReportLow.summary}
                details={sampleReportLow.details}
                analysis={sampleReportLow.analysis}
              />
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </>
  );
}
