"use client"

import { motion } from "framer-motion"
import { PieChart, Pie, Cell, ResponsiveContainer, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip } from "recharts"
import { AlertTriangle, CheckCircle, Activity, Shield, FileText, TrendingUp, Users, Calendar, Stethoscope, Award, Info, ChevronDown, ChevronUp, Download, Share2 } from "lucide-react"
import { AnimatedCard } from "./animated-card"
import { useState, useEffect } from "react"
import { Button } from "./button"

interface ResultVisualizationProps {
  probability: number
  summary: string
  details: string
  analysis?: string
}

interface AnalysisData {
  probability_percent: number
  risk_level: string
  iacs_level: string
  rationale: string[]
  next_steps: string
  disclaimer: string
}

export function ResultVisualization({ probability, summary, details, analysis }: ResultVisualizationProps) {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    rationale: true, // Start with rationale expanded for better UX
    nextSteps: true  // Start with next steps expanded for better UX
  })
  const [parsedAnalysis, setParsedAnalysis] = useState<AnalysisData | null>(null)

  // Parse analysis on component mount
  useEffect(() => {
    if (analysis) {
      try {
        const parsed = JSON.parse(analysis)
        setParsedAnalysis(parsed)
      } catch (error) {
        console.error("Failed to parse analysis:", error)
      }
    }
  }, [analysis])

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }))
  }

  const getRiskLevel = (prob: number) => {
    if (prob >= 75) return {
      level: "Alto",
      color: "red",
      icon: AlertTriangle,
      bgColor: "bg-destructive/10",
      textColor: "text-destructive",
      borderColor: "border-destructive/20"
    }
    if (prob >= 50) return {
      level: "Moderato",
      color: "yellow",
      icon: Activity,
      bgColor: "bg-warning/10",
      textColor: "text-warning",
      borderColor: "border-warning/20"
    }
    if (prob >= 25) return {
      level: "Basso",
      color: "blue",
      icon: CheckCircle,
      bgColor: "bg-primary/10",
      textColor: "text-primary",
      borderColor: "border-primary/20"
    }
    return {
      level: "Molto Basso",
      color: "green",
      icon: CheckCircle,
      bgColor: "bg-success/10",
      textColor: "text-success",
      borderColor: "border-success/20"
    }
  }

  const risk = getRiskLevel(parsedAnalysis?.probability_percent || probability)
  const RiskIcon = risk.icon

  // Enhanced data for visualizations
  const pieData = [
    {
      name: "Probabilità",
      value: parsedAnalysis?.probability_percent || probability,
      color: `hsl(var(--${risk.color === "red" ? "destructive" : risk.color === "yellow" ? "warning" : risk.color === "blue" ? "primary" : "success"}))`
    },
    {
      name: "Restante",
      value: 100 - (parsedAnalysis?.probability_percent || probability),
      color: "hsl(var(--muted))"
    }
  ]

  // Enhanced symptom analysis data
  const symptomData = [
    { name: "Prurito Notturno", value: probability > 50 ? 85 : 30, category: "Sintomo Primario" },
    { name: "Eruzioni Cutanee", value: probability > 50 ? 70 : 25, category: "Manifestazione" },
    { name: "Cunicoli Visibili", value: probability > 50 ? 60 : 15, category: "Segno Diagnostico" },
    { name: "Contatti a Rischio", value: probability > 50 ? 75 : 20, category: "Fattore di Rischio" },
    { name: "Localizzazione Tipica", value: probability > 50 ? 80 : 35, category: "Distribuzione" },
  ]

  // Risk factors breakdown
  const riskFactorsData = [
    { factor: "Sintomi", score: probability > 50 ? 8 : 4, maxScore: 10 },
    { factor: "Localizzazione", score: probability > 50 ? 7 : 3, maxScore: 10 },
    { factor: "Storia Clinica", score: probability > 50 ? 6 : 2, maxScore: 10 },
    { factor: "Contatti", score: probability > 50 ? 5 : 1, maxScore: 10 },
  ]

  return (
    <div className="space-y-12">
      {/* Professional Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative overflow-hidden"
      >
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-background to-secondary/5 rounded-3xl" />

        {/* Content */}
        <div className="relative p-12 text-center space-y-8">
          {/* Medical badge */}
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="inline-flex items-center gap-3 bg-white/80 backdrop-blur-sm border border-primary/20 rounded-full px-6 py-3 shadow-lg"
          >
            <Stethoscope className="w-5 h-5 text-primary" />
            <span className="text-sm font-semibold text-primary">Analisi Medica Completata</span>
            <Award className="w-5 h-5 text-primary" />
          </motion.div>

          {/* Main result display */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.4, type: "spring", stiffness: 150 }}
            className="space-y-4"
          >
            <div className={`inline-flex items-center gap-4 ${risk.bgColor} ${risk.borderColor} border-2 rounded-2xl px-8 py-6 shadow-lg`}>
              <RiskIcon className={`w-12 h-12 ${risk.textColor}`} />
              <div className="text-left">
                <div className="text-3xl font-bold text-foreground">
                  {parsedAnalysis?.probability_percent || probability}%
                </div>
                <div className={`text-lg font-semibold ${risk.textColor}`}>
                  Rischio {parsedAnalysis?.risk_level || risk.level}
                </div>
                {parsedAnalysis?.iacs_level && (
                  <div className="text-sm text-muted-foreground">
                    Livello IACS: {parsedAnalysis.iacs_level}
                  </div>
                )}
              </div>
            </div>
          </motion.div>

          {/* Professional title */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="space-y-3"
          >
            <h1 className="text-4xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Rapporto di Valutazione Scabbia
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              Analisi professionale basata sui criteri diagnostici IACS 2020 e linee guida CDC
            </p>
          </motion.div>

          {/* Action buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="flex flex-wrap justify-center gap-4"
          >
            <Button variant="outline" className="gap-2">
              <Download className="w-4 h-4" />
              Scarica Rapporto
            </Button>
            <Button variant="outline" className="gap-2">
              <Share2 className="w-4 h-4" />
              Condividi Risultati
            </Button>
          </motion.div>
        </div>
      </motion.div>

      {/* Enhanced Analysis Dashboard */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Main Probability Gauge */}
        <AnimatedCard delay={0.2} className="lg:col-span-2 p-8">
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">Analisi Probabilistica</h2>
              <p className="text-muted-foreground">Valutazione basata su criteri clinici standardizzati</p>
            </div>

            {/* Enhanced Gauge Chart */}
            <div className="relative h-80">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    startAngle={180}
                    endAngle={0}
                    innerRadius={80}
                    outerRadius={140}
                    dataKey="value"
                    stroke="none"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>

              {/* Enhanced center display */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 1.0, type: "spring" }}
                className="absolute inset-0 flex items-center justify-center"
              >
                <div className="text-center space-y-2">
                  <div className={`text-5xl font-bold ${risk.textColor}`}>
                    {parsedAnalysis?.probability_percent || probability}%
                  </div>
                  <div className="text-lg font-semibold text-foreground">
                    {parsedAnalysis?.risk_level || risk.level}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Livello di Rischio
                  </div>
                </div>
              </motion.div>

              {/* Risk level indicator */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1.2 }}
                className="absolute bottom-4 left-1/2 transform -translate-x-1/2"
              >
                <div className={`px-4 py-2 rounded-full ${risk.bgColor} ${risk.borderColor} border`}>
                  <span className={`text-sm font-medium ${risk.textColor}`}>
                    {parsedAnalysis?.iacs_level || "Valutazione Clinica"}
                  </span>
                </div>
              </motion.div>
            </div>
          </div>
        </AnimatedCard>

        {/* Risk Factors Breakdown */}
        <AnimatedCard delay={0.4} className="p-6">
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-xl font-bold text-foreground mb-2">Fattori di Rischio</h3>
              <p className="text-sm text-muted-foreground">Analisi dei componenti</p>
            </div>

            <div className="space-y-4">
              {riskFactorsData.map((item, index) => (
                <motion.div
                  key={item.factor}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  className="space-y-2"
                >
                  <div className="flex justify-between text-sm">
                    <span className="font-medium text-foreground">{item.factor}</span>
                    <span className="text-muted-foreground">{item.score}/{item.maxScore}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${(item.score / item.maxScore) * 100}%` }}
                      transition={{ delay: 0.8 + index * 0.1, duration: 0.8 }}
                      className={`h-2 rounded-full ${
                        item.score >= 7 ? 'bg-destructive' :
                        item.score >= 5 ? 'bg-warning' :
                        item.score >= 3 ? 'bg-primary' : 'bg-success'
                      }`}
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </AnimatedCard>
      </div>

      {/* Professional Analysis Section */}
      {parsedAnalysis && (
        <AnimatedCard delay={0.6} className="p-8">
          <div className="space-y-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-foreground mb-2">Analisi Clinica Dettagliata</h2>
              <p className="text-muted-foreground">Valutazione professionale basata sui tuoi sintomi</p>
            </div>

            {/* Clinical Rationale */}
            <div className="space-y-4">
              <div
                className="flex items-center justify-between cursor-pointer p-4 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors"
                onClick={() => toggleSection('rationale')}
              >
                <div className="flex items-center gap-3">
                  <FileText className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">Razionale Clinico</h3>
                </div>
                {expandedSections.rationale ?
                  <ChevronUp className="w-5 h-5 text-muted-foreground" /> :
                  <ChevronDown className="w-5 h-5 text-muted-foreground" />
                }
              </div>

              {expandedSections.rationale && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-3 p-4 bg-card border border-border rounded-lg"
                >
                  {parsedAnalysis.rationale.map((point, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-start gap-3"
                    >
                      <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                      <p className="text-foreground leading-relaxed">{point}</p>
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </div>

            {/* Next Steps */}
            <div className="space-y-4">
              <div
                className="flex items-center justify-between cursor-pointer p-4 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors"
                onClick={() => toggleSection('nextSteps')}
              >
                <div className="flex items-center gap-3">
                  <TrendingUp className="w-5 h-5 text-primary" />
                  <h3 className="text-lg font-semibold text-foreground">Raccomandazioni</h3>
                </div>
                {expandedSections.nextSteps ?
                  <ChevronUp className="w-5 h-5 text-muted-foreground" /> :
                  <ChevronDown className="w-5 h-5 text-muted-foreground" />
                }
              </div>

              {expandedSections.nextSteps && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="p-4 bg-card border border-border rounded-lg"
                >
                  <div className={`p-4 rounded-lg ${risk.bgColor} ${risk.borderColor} border`}>
                    <div className="flex items-start gap-3">
                      <Info className={`w-5 h-5 ${risk.textColor} mt-0.5 flex-shrink-0`} />
                      <p className="text-foreground leading-relaxed font-medium">
                        {parsedAnalysis.next_steps}
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </AnimatedCard>
      )}

      {/* Symptom Analysis Visualization */}
      <AnimatedCard delay={0.8} className="p-8">
        <div className="space-y-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-foreground mb-2">Analisi dei Sintomi</h3>
            <p className="text-muted-foreground">Distribuzione e intensità dei fattori valutati</p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Radar Chart */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-center">Profilo Sintomatico</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart data={symptomData}>
                    <PolarGrid stroke="hsl(var(--border))" />
                    <PolarAngleAxis
                      dataKey="name"
                      tick={{ fontSize: 12, fill: "hsl(var(--muted-foreground))" }}
                    />
                    <PolarRadiusAxis
                      angle={90}
                      domain={[0, 100]}
                      tick={{ fontSize: 10, fill: "hsl(var(--muted-foreground))" }}
                    />
                    <Radar
                      name="Intensità"
                      dataKey="value"
                      stroke="hsl(var(--primary))"
                      fill="hsl(var(--primary))"
                      fillOpacity={0.3}
                      strokeWidth={3}
                    />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Bar Chart */}
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-center">Intensità per Categoria</h4>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={symptomData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 10, fill: "hsl(var(--muted-foreground))" }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis
                      tick={{ fontSize: 10, fill: "hsl(var(--muted-foreground))" }}
                      domain={[0, 100]}
                    />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "hsl(var(--card))",
                        border: "1px solid hsl(var(--border))",
                        borderRadius: "8px"
                      }}
                    />
                    <Bar
                      dataKey="value"
                      fill="hsl(var(--primary))"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>
      </AnimatedCard>

      {/* Professional Summary Section */}
      <div className="grid lg:grid-cols-3 gap-8">
        {/* Executive Summary */}
        <AnimatedCard delay={1.0} className="lg:col-span-2 p-8">
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-3 bg-primary/10 rounded-lg">
                <FileText className="w-6 h-6 text-primary" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-foreground">Riepilogo Esecutivo</h3>
                <p className="text-sm text-muted-foreground">Sintesi della valutazione clinica</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="p-6 bg-gradient-to-r from-muted/30 to-muted/10 rounded-lg border border-border">
                <h4 className="font-semibold text-foreground mb-3">Valutazione Generale</h4>
                <p className="text-foreground leading-relaxed">{summary}</p>
              </div>

              <div className="p-6 bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg border border-primary/20">
                <h4 className="font-semibold text-foreground mb-3">Dettagli Clinici</h4>
                <p className="text-foreground leading-relaxed">{details}</p>
              </div>
            </div>
          </div>
        </AnimatedCard>

        {/* Key Metrics */}
        <AnimatedCard delay={1.2} className="p-6">
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-success/10 rounded-lg">
                <TrendingUp className="w-5 h-5 text-success" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-foreground">Metriche Chiave</h3>
                <p className="text-sm text-muted-foreground">Indicatori principali</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-card border border-border rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-muted-foreground">Probabilità</span>
                  <span className={`text-lg font-bold ${risk.textColor}`}>
                    {parsedAnalysis?.probability_percent || probability}%
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className={`h-2 rounded-full ${
                      (parsedAnalysis?.probability_percent || probability) >= 75 ? 'bg-destructive' :
                      (parsedAnalysis?.probability_percent || probability) >= 50 ? 'bg-warning' :
                      (parsedAnalysis?.probability_percent || probability) >= 25 ? 'bg-primary' : 'bg-success'
                    }`}
                    style={{ width: `${parsedAnalysis?.probability_percent || probability}%` }}
                  />
                </div>
              </div>

              <div className="p-4 bg-card border border-border rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-foreground mb-1">
                    {parsedAnalysis?.risk_level || risk.level}
                  </div>
                  <div className="text-sm text-muted-foreground">Livello di Rischio</div>
                </div>
              </div>

              {parsedAnalysis?.iacs_level && (
                <div className="p-4 bg-card border border-border rounded-lg">
                  <div className="text-center">
                    <div className="text-lg font-semibold text-foreground mb-1">
                      {parsedAnalysis.iacs_level}
                    </div>
                    <div className="text-sm text-muted-foreground">Classificazione IACS</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </AnimatedCard>
      </div>

      {/* Professional Methodology Section */}
      <AnimatedCard delay={1.4} className="p-8">
        <div className="space-y-6">
          <div className="text-center">
            <div className="flex items-center justify-center gap-3 mb-4">
              <Shield className="w-6 h-6 text-primary" />
              <h3 className="text-xl font-bold text-foreground">Metodologia e Affidabilità</h3>
              <Shield className="w-6 h-6 text-primary" />
            </div>
            <p className="text-muted-foreground">Criteri scientifici utilizzati per la valutazione</p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-gradient-to-br from-primary/5 to-primary/10 rounded-lg border border-primary/20">
              <Calendar className="w-8 h-8 text-primary mx-auto mb-3" />
              <h4 className="font-semibold text-foreground mb-2">Criteri IACS 2020</h4>
              <p className="text-sm text-muted-foreground">Standard internazionali per la diagnosi di scabbia</p>
            </div>

            <div className="text-center p-6 bg-gradient-to-br from-secondary/5 to-secondary/10 rounded-lg border border-secondary/20">
              <Users className="w-8 h-8 text-secondary-accent mx-auto mb-3" />
              <h4 className="font-semibold text-foreground mb-2">Linee Guida CDC</h4>
              <p className="text-sm text-muted-foreground">Protocolli dei Centers for Disease Control</p>
            </div>

            <div className="text-center p-6 bg-gradient-to-br from-success/5 to-success/10 rounded-lg border border-success/20">
              <Stethoscope className="w-8 h-8 text-success mx-auto mb-3" />
              <h4 className="font-semibold text-foreground mb-2">Validazione Clinica</h4>
              <p className="text-sm text-muted-foreground">Algoritmo validato su dati clinici reali</p>
            </div>
          </div>
        </div>
      </AnimatedCard>

      {/* Enhanced Professional Disclaimer */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.6 }}
        className="relative"
      >
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-r from-warning/5 via-warning/10 to-warning/5 rounded-2xl" />

        {/* Content */}
        <div className="relative p-8 border border-warning/20 rounded-2xl">
          <div className="flex items-start gap-4">
            <div className="p-3 bg-warning/10 rounded-lg flex-shrink-0">
              <AlertTriangle className="w-6 h-6 text-warning" />
            </div>
            <div className="space-y-4">
              <h3 className="text-lg font-bold text-foreground">Importante Disclaimer Medico</h3>

              <div className="space-y-3 text-sm text-foreground leading-relaxed">
                <p>
                  <strong>Questa analisi è uno strumento di supporto informativo</strong> e non sostituisce in alcun modo
                  la consulenza, diagnosi o trattamento medico professionale.
                </p>

                <p>
                  I risultati sono basati su algoritmi clinici standardizzati (IACS 2020, CDC) ma
                  <strong> richiedono sempre conferma da parte di un medico qualificato</strong>.
                </p>

                <p>
                  {parsedAnalysis?.disclaimer ||
                    "Si raccomanda vivamente di consultare un dermatologo o medico di base per una valutazione clinica completa e una diagnosi definitiva."
                  }
                </p>
              </div>

              <div className="flex flex-wrap gap-3 pt-2">
                <span className="px-3 py-1 bg-warning/10 text-warning text-xs font-medium rounded-full">
                  Solo a scopo informativo
                </span>
                <span className="px-3 py-1 bg-primary/10 text-primary text-xs font-medium rounded-full">
                  Consultare sempre un medico
                </span>
                <span className="px-3 py-1 bg-success/10 text-success text-xs font-medium rounded-full">
                  Basato su standard clinici
                </span>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
